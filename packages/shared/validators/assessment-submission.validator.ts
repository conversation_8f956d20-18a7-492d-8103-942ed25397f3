import * as z from "zod";
import { zContact, zString, zNumber } from ".";
import {
  AuditorScore,
  CompanyExceptionApproval,
  VendorAcceptanceStatus,
  VulnerabilityResolveStatus,
  VulnerabilityClosureStatus,
  Source,
} from "../types/AssessmentSubmission";
import { CRITICALITY_LEVELS } from "../types/Company";

export const updateQuestionsSubmissionStateValidator = z.object({
  assessment: zString,
  questionsState: z.unknown().optional(),
});

export const getUpdateQuestionsSubmissionStateValidator = z.object({
  assessment: zString,
});

export const userAssessmentSubmissionValidator = z
  .array(
    z.object({
      assessment: zString,
      vendor: zString,
      submission: z.object({
        question: z.string(),
        answer: z.any(),
        files: z.array(zString).optional(),
      }),
      user: zString,
    })
  )
  .min(1);

export const getAssessmentSubmissionsValidator = z.object({
  assessment: zString.optional(),
  vendor: zString.optional(),
  user: zString.optional(),
  questions: z.array(zString).optional(),
  CRITICALITY_LEVELS: z.nativeEnum(CRITICALITY_LEVELS).optional(),
});

export const manageVulnerabilityScoreValidator = z
  .object({
    submissionId: zString,
    score: zString,
    cvssScore: zString,
    remarks: z.string().optional(),
    criticalityLevel: z.nativeEnum(CRITICALITY_LEVELS),
    sendEmail: z.boolean().optional(),
    vulnerabilityEvidence: z.string().optional(),
    cvssComparison: z.string().optional(),
    source: z.nativeEnum(Source).optional(),
    sourceOther: z.string().optional(),
  })
  .superRefine(({ source, sourceOther }, ctx) => {
    if (source === Source.OTHER && !sourceOther) {
      ctx.addIssue({
        code: "custom",
        message: "Source other is required",
        path: ["sourceOther"],
      });
    }
    if (source === Source.VENDOR) {
      ctx.addIssue({
        code: "custom",
        message: "Comparison is required",
        path: ["cvssComparison"],
      });
    }
  });

//for auditor
export const manageAuditorScoreValdator = z.object({
  submissionId: zString,
  score: z.string(),
  remarks: z.string().optional(),
});

export const sendSubmissionScoreCardValidator = z.object({
  assessment: zString,
  vendor: zString,
  message: zString.optional(),
});

export const vendorAcceptanceValidator = z
  .object({
    submissionId: zString,
    vendorAcceptanceStatus: z.nativeEnum(VendorAcceptanceStatus),
    vendorRejectReason: z.string().optional(),
    rejectionEvidence: z.string().optional(),
  })
  .superRefine(({ vendorAcceptanceStatus, rejectionEvidence }, ctx) => {
    if (
      vendorAcceptanceStatus === VendorAcceptanceStatus.REJECT &&
      !rejectionEvidence
    ) {
      ctx.addIssue({
        code: "custom",
        message: "Rejection evidence is required",
        path: ["rejectionEvidence"],
      });
    }
  });

export const getAssessmentSubmissionHistoryValidator = z.object({
  question: z.string().optional(),
});

/** Vulnerability Resolve */
export const updateVulnerabilityResolveStatusByEmployeeValidator = z.object({
  submissionId: zString,
  resolveStatus: z.nativeEnum(VulnerabilityResolveStatus),
  resolveDescription: z.string().optional(),
  vulnerabilityResolvedOrExceptionEvidence: z.string().optional(),
});

export const requestVulnerabilityExceptionSocToCompanyValidator = z.object({
  submissionId: zString,
  vulnerabilityExceptionApprovalDescriptionBySoc: z.string().optional(),
  vulnerabilityExceptionApprovalEvidenceBySoc: z.string().optional(),
});

export const requestVulnerabilityExceptionCompanyToSocApprovalValidator =
  z.object({
    submissionId: zString,
    companyExceptionApprovalEvidence: z.string().optional(),
    companyExceptionApproval: z.nativeEnum(CompanyExceptionApproval),
    companyExceptionApprovalDescripton: z.string().optional(),
    companyExceptionEndDate: z.date().optional(),
    companyExceptionApprovedDate: z.date().optional(),
  });

export const approvedVulnerabilityExceptionSocToVendorValidator = z.object({
  submissionId: zString,
  vulnerabilityExceptionStatusDateSocToVendor: z.date().optional(),
  vulnerabilityExceptionStatusSocToVendor: z.string().optional(),
});

export const updateAssetForAssessmentValidator = z.object({
  _id: zString,
  asset: zString,
});

/** Vulnerability Closure */
export const requestVulnerabilityClosureBySocToCompanyValidator = z.object({
  submissionId: zString,
  vulnerabilityClosureDescriptionBySoc: z.string().optional(),
  vulnerabilityClosureEvidenceBySoc: z.string().optional(),
});

export const approveVulnerabilityClosureRequestByCompanyToSocValidator =
  z.object({
    submissionId: zString,
    vulnerabilityClosureCompanyApprovalStatus: z.nativeEnum(
      VulnerabilityClosureStatus
    ),
    vulnerabilityClosureEvidenceByCompany: z.string().optional(),
    vulnerabilityClosureDateByCompany: z.date().optional(),
    vulnerabilityClosureDescriptionByCompany: z.string().optional(),
  });

export const updateVulnerabilityClosureBySocValidator = z.object({
  submissionId: zString,
  vulnerabilityClosureStatus: z.nativeEnum(VulnerabilityClosureStatus),
  vulnerabilityClosureDateBySoc: z.date().optional(),
  vulnerabilityClosedDescriptionBySoc: z.string().optional(),
});
