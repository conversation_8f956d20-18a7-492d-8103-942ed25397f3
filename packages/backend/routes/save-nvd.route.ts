import {
  getNVDDataValidator,
  saveNVDDataValidator,
} from "../../shared/validators/save-nvd.validator";
import {
  getNVDAllData,
  getNVDData,
  saveNVDData,
} from "../controllers/nvd.saving.controller";
import authProcedure from "../middleware/auth";
import { t } from "../trpc";

export const saveNVDRoute = t.router({
  saveNVDData: authProcedure()
    .input(saveNVDDataValidator)
    .mutation(saveNVDData),
  getNVDData: authProcedure().input(getNVDDataValidator).query(getNVDData),
  getNVDAllData: authProcedure()
    .input(getNVDDataValidator)
    .query(getNVDAllData),
});
