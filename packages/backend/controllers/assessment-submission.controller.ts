import { render } from "@react-email/render";
import { TRPCError } from "@trpc/server";
import { addDays, format } from "date-fns";
import { ASSIGNMENT_STATUS } from "../../shared/types/Assesment";
import {
  Company,
  CompanyType,
  CriticalityLevel,
} from "../../shared/types/Company";
import { Section, SECTIONTYPE } from "../../shared/types/Standard";
import { USER_ROLE } from "../../shared/types/User";
import VulnerabilityTemplate from "../../transactional/emails/vulnerability";
import VulnerabilityAcceptanceMail from "../../transactional/emails/vulnerability.accept-reject";
import VulnerabilityResolveExceptionMail from "../../transactional/emails/vulnerability.resolve-exception";
import VulnerabilityExceptionApprovalMail from "../../transactional/emails/vulnerability.exception-approval";
import VulnerabilityExceptionApprovalStatusByCompanyMail from "../../transactional/emails/vulnerability.exception-approval-status";
import VulnerabilityClosureApprovalSocToCompanyMail from "../../transactional/emails/vulnerability.closure-approval";
import VulnerabilityClosureApprovalStatusByCompanyMail from "../../transactional/emails/vulnerability.closure-approval-status";
import VulnerabilityClose from "../../transactional/emails/vulnerability.closed";
import AssessmentCompletedMail from "../../transactional/emails/assessment.completed.mail";
import VulnerabilityApprovedExceptionBycompanyToVendor from "../../transactional/emails/vulnerability.exception-approved-to-employee";

import { MD } from "../@types";
import { ATC } from "../@types/Trpc";
import {
  AssessmentSubmissionModel,
  QuestionsSubmissionStateModel,
} from "../models/asessment-submission.model";
import { AsessmentSubmissionHistoryModel } from "../models/assessment-submission-history.model";
import { AssessmentModel } from "../models/assessment.model";
import { CompanyModel } from "../models/company.model";
import { LinkModel } from "../models/link.model";
import { QuestionModel } from "../models/questions.model";
import { UserModel } from "../models/user.model";
import { sendEmail } from "../utils/email";
import {
  approveVulnerabilityClosureRequestByCompanyToSocValidator,
  approvedVulnerabilityExceptionSocToVendorValidator,
  getAssessmentSubmissionsValidator,
  getUpdateQuestionsSubmissionStateValidator,
  manageAuditorScoreValdator,
  manageVulnerabilityScoreValidator,
  requestVulnerabilityClosureBySocToCompanyValidator,
  requestVulnerabilityExceptionCompanyToSocApprovalValidator,
  requestVulnerabilityExceptionSocToCompanyValidator,
  sendSubmissionScoreCardValidator,
  updateQuestionsSubmissionStateValidator,
  updateVulnerabilityClosureBySocValidator,
  updateVulnerabilityResolveStatusByEmployeeValidator,
  userAssessmentSubmissionValidator,
  vendorAcceptanceValidator,
} from "../../shared/validators/assessment-submission.validator";
import { Question } from "../../shared/types/Question";
import { FilterQuery } from "mongoose";
import _ from "lodash";
import {
  CompanyExceptionApproval,
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
} from "../../shared/types/AssessmentSubmission";
import { createJwtByUserEmail } from "../utils/jwt";

// Submissions Save
export const updateQuestionsSubmissionState = ({
  input: { assessment, questionsState },
  ctx,
}: ATC<typeof updateQuestionsSubmissionStateValidator>) =>
  QuestionsSubmissionStateModel.updateOne(
    {
      assessment,
      user: ctx.user._id,
    },
    { assessment, user: ctx.user._id, questionsState },
    { upsert: true }
  );

export const getUpdateQuestionsSubmissionState = ({
  ctx,
  input: { assessment },
}: ATC<typeof getUpdateQuestionsSubmissionStateValidator>) =>
  QuestionsSubmissionStateModel.findOne({
    assessment,
    user: ctx.user._id,
  }).lean();

// Submissions
export const getAssessmentSubmissions = async ({
  input: { questions, assessment, user, vendor },
}: ATC<typeof getAssessmentSubmissionsValidator>) => {
  let filter: FilterQuery<Question> = _.omitBy(
    { assessment, user, vendor },
    _.isUndefined
  );
  if (questions?.length) {
    filter["submission.question"] = { $in: questions };
  }

  const res = await AssessmentSubmissionModel.find(filter)
    .populate<{
      vendor?: { company: string; criticalityLevels?: CriticalityLevel[] };
      submission: {
        question?: MD<Question> & { section: Section };
        answer: string;
      };
      assessment: {};
      cvssComparison?: {};
    }>([
      { path: "vendor", select: "company criticalityLevels" },
      {
        path: "submission",
        populate: { path: "question", populate: { path: "section" } },
      },
      { path: "assessment" },
      {
        path: "cvssComparison",
        populate: {
          path: "cvssCalculator",
          select:
            "name cvssMetrics riskInputs riskResults cvssVector securityRiskOverview vendorAIScore calculationDate mitigationControls",
        },
      },
    ])
    .lean()
    .sort({ createdAt: -1 });

  return res;
};

// Submission
export const userAssessmentSubmission = async ({
  input,
}: ATC<typeof userAssessmentSubmissionValidator>) => {
  // check if user already submitted his assessment
  const [
    {
      assessment: assessmentId,
      user: userId,
      vendor: vendorId,
      submission: submissionID,
    },
  ] = input;
  const assessment = await AssessmentModel.findById(assessmentId).populate(
    "sections"
  );

  if (!assessment)
    throw new TRPCError({ code: "NOT_FOUND", message: "assessment not foud" });
  const submissionExists = await AssessmentSubmissionModel.exists({
    assessment: assessmentId,
    user: userId,
  });

  if (submissionExists)
    throw new TRPCError({
      code: "CONFLICT",
      message: "assessment already submitted by user",
    });

  // update assessment status
  const assignments = assessment.assignments.map((assignment) => {
    if (String(assignment.employee) === userId) {
      assignment.assignmentStatus = ASSIGNMENT_STATUS.COMPLETED;
      assignment.assignmentCompletedDate = new Date();
    }

    return assignment;
  });

  await AssessmentModel.findByIdAndUpdate(assessmentId, { assignments });

  const questions = await QuestionModel.find({
    _id: { $in: input.map((_) => _.submission.question) },
  })
    .populate<{ section: MD<Section> }>([{ path: "section" }])
    .lean();

  await AssessmentSubmissionModel.bulkSave(
    input.map((sub) => {
      const sectionType = questions.find(
        (q) => String(q._id) === String(sub.submission.question)
      )?.section.sectionType;
      const isGeneralQuestion = sectionType === SECTIONTYPE.GENERAL;

      const questionType = questions.find(
        (q) => String(q._id) === String(sub.submission.question)
      )?.input.inputType;

      const isTextInput = questionType === "text";

      const isCheckboxInput = questionType === "checkbox";

      const questionAnswer = questions.find(
        (q) => String(q._id) === String(sub.submission.question)
        // @ts-ignore
      )?.input?.answer;

      const submissionModelinput = {
        ...sub,
        sectionType,
      };

      const answer = Array.isArray(sub.submission.answer)
        ? sub.submission.answer
        : [sub.submission.answer];

      let score = 0;

      // Check if 'code:v-E42GGKAS3' is in the answer
      if (answer.includes("code:v-E42GGKAS3")) {
        score = 1;
        // @ts-expect-error
        submissionModelinput.audit = {
          score,
        };
      }

      if (isGeneralQuestion && !isTextInput) {
        const comparision = Array.isArray(questionAnswer)
          ? questionAnswer
          : [questionAnswer];

        // Check if the answer matches the comparison array
        if (
          answer.length === comparision.length &&
          answer.every((a) => comparision.includes(a))
        ) {
          score = 1;
        }
        // @ts-ignore
        submissionModelinput.audit = {
          score,
        };
      }

      return new AssessmentSubmissionModel(submissionModelinput);
    })
  );

  // Save History
  await AsessmentSubmissionHistoryModel.bulkSave(
    input.map(
      (sub) =>
        new AsessmentSubmissionHistoryModel({
          assessment: assessmentId,
          actionTaker: userId,
          vendor: vendorId,
          message: `Question Submitted`,
          question: sub.submission.question,
        })
    )
  );

  const vendor = await CompanyModel.findById(vendorId).populate<{
    reportsTo: Company;
  }>("reportsTo");
  if (!vendor)
    throw new TRPCError({ code: "NOT_FOUND", message: "vendor not found" });
  if (vendor?.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "vendor must be a vendor",
    });

  // Send mail to vendor, company and soc
  const users = await UserModel.find({
    companies: { $in: [vendorId, vendor.reportsTo] },
    role: {
      $in: [
        USER_ROLE.VENDOR,
        USER_ROLE.COMPANY,
        USER_ROLE.LEAD_SOC_ANALYST,
        USER_ROLE.SOC_ANALYST,
      ],
    },
  }).lean();

  const employee = await UserModel.findById(userId);

  const emails = users.map(($) => $.email);

  // create links
  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: assessmentId,
      questions: questions.map((q) => String(q._id)),
    },
  }).save();

  // Send emails
  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      await sendEmail({
        to: email,
        subject: "VENDOR AI | VENDOR ASSESSMENT SUBMITTED",
        html: render(
          AssessmentCompletedMail({
            link: String(link._id),
            token: token,
            assessment: assessment.name,
            vendor: vendor.company,
            company: vendor.reportsTo.company,
            vendorRespondent: employee?.email,
          })
        ),
      });
    });
  }
};

// Scoring inventory questions Send MAil BY SOC
export const manageVulnerabilityScoreBySocAnalyst = async ({
  ctx,
  input,
}: ATC<typeof manageVulnerabilityScoreValidator>) => {
  const { submissionId, sendEmail: canSendMail, ...other } = input;

  const submission = await AssessmentSubmissionModel.findById(submissionId);

  if (!submission)
    throw new TRPCError({ code: "NOT_FOUND", message: "submission not found" });
  if (submission.sectionType !== SECTIONTYPE.INVENTORY)
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "must be a inventory submission",
    });

  const vendor = await CompanyModel.findById(submission.vendor);

  let vulnerabilityExpirationDate: Date | undefined = undefined;

  if (vendor && vendor.type === CompanyType.VENDOR) {
    const expiryInDays = vendor.criticalityLevels.find(
      ({ level }) => level === other.criticalityLevel
    )?.timeDuration;

    if (expiryInDays)
      vulnerabilityExpirationDate = addDays(new Date(), expiryInDays);
  }

  submission.vulnerability = {
    analysisBy: ctx.user._id,
    ...other,
    ...(vulnerabilityExpirationDate && { expiry: vulnerabilityExpirationDate }),
  };
  submission.cvssComparison = input.cvssComparison;

  // Add to assessment history
  new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    submission: submissionId,
    actionTaker: ctx.user._id,
    vendor: submission.vendor,
    message: `Assessment scored by soc-analyst - CRITICALITY_LEVEL:${input.criticalityLevel} and score:${input.score} and cvssScore:${input.cvssScore}`,
    question: submission.submission.question,
    sectionType: submission,
  }).save();

  await submission.save();

  // Send emails
  if (canSendMail) {
    const usersToFind: string[] = [];

    if (vendor?._id) usersToFind.push(String(vendor._id)); // vendor id
    if (vendor?._id && vendor.type === CompanyType.VENDOR)
      usersToFind.push(String(vendor.reportsTo)); // company id
    // if (submission.user) usersToFind.push(String(submission.user)); //employee id
    const users = await UserModel.find({
      companies: { $in: usersToFind },
      role: {
        $in: [
          USER_ROLE.VENDOR,
          USER_ROLE.COMPANY,
          USER_ROLE.LEAD_SOC_ANALYST,
          USER_ROLE.SOC_ANALYST,
          USER_ROLE.EMPLOYEE,
        ],
      },
    }).lean();
    const employee = await UserModel.findById(submission.user);
    const emails = [...users.map(($) => $.email), employee?.email];
    // create a expirable link
    const link = await new LinkModel({
      expiryDate: addDays(new Date(), 15),
      metadata: {
        type: "assessment",
        assessment: submission.assessment,
        questions: [submission.submission.question],
        role: USER_ROLE.EMPLOYEE,
      },
    }).save();

    // send emails
    if (emails.length) {
      emails.forEach(async (email) => {
        const token = await createJwtByUserEmail(email as string);
        await sendEmail({
          to: email,
          subject:
            "Urgent: Security Vulnerability Identified in Vendor Equipment",
          html: render(
            VulnerabilityTemplate({
              link: String(link._id),
              daysToResolve: format(
                submission?.vulnerability?.expiry as Date,
                "PPP"
              ),
              severityLevel: input.criticalityLevel.toUpperCase(),
              vulnerabilityDescription: input.remarks,
              token: token,
            })
          ),
        });
      });
    }
  }
};

export const manageAuditScoreByAuditor = async ({
  ctx,
  input,
}: ATC<typeof manageAuditorScoreValdator>) => {
  const { submissionId, ...rest } = input;

  const submission = await AssessmentSubmissionModel.findById(submissionId);

  if (!submission)
    throw new TRPCError({ code: "NOT_FOUND", message: "submission not found" });

  if (submission.sectionType !== SECTIONTYPE.GENERAL)
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "must be a general submission",
    });

  submission.audit = { auditedBy: ctx.user._id, ...rest };

  // TODO: __HISTORY
  // new AsessmentSubmissionHistoryModel({
  //   assessment: submission.assessment,
  //   submission: submissionId,
  //   from: ctx.user._id,
  //   vendor: submission.vendor,
  //   message: `Audit status for submitted questions `,
  //   question: submission.submission.question,
  // }).save();

  return submission.save();
};

//Auditor + Soc SCORE CARD
export const sendSubmissionsScoreCard = async ({
  input,
  ctx,
}: ATC<typeof sendSubmissionScoreCardValidator>) => {
  const isAuditor = ctx.user.role === USER_ROLE.AUDITOR;

  const { assessment, vendor } = input;
  const vendorDoc = await CompanyModel.findById(vendor).lean();
  if (!vendorDoc) throw new TRPCError({ code: "NOT_FOUND" });

  const assessmentSubmissions = await AssessmentSubmissionModel.find({
    vendor,
    assessment,
  });

  const questionsScored = assessmentSubmissions
    .filter((submission) =>
      isAuditor
        ? submission.sectionType == SECTIONTYPE.GENERAL
        : submission.sectionType == SECTIONTYPE.INVENTORY
    )
    .every((submission) =>
      isAuditor
        ? submission.sectionType == SECTIONTYPE.GENERAL &&
          submission?.audit?.score
        : submission.sectionType == SECTIONTYPE.INVENTORY &&
          submission?.vulnerability?.score
    );

  if (!questionsScored) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "score all questions to raise a score card",
    });
  }

  const usersToFind = [vendor];
  if (vendorDoc.type === CompanyType.VENDOR)
    usersToFind.push(String(vendorDoc._id));

  const users = await UserModel.find({
    companies: { $in: usersToFind },
    role: {
      $in: [USER_ROLE.VENDOR, USER_ROLE.COMPANY],
    },
  }).lean();
  const emails = users.map(($) => $.email);
  // create a expirable link
  const link = await new LinkModel({
    expiryDate: addDays(new Date(), 15),
    metadata: { type: "assessment", assessment },
  }).save();

  // send emails
  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      await sendEmail({
        to: email,
        subject: "VENDOR AI | VULNERABILITY SCORE ANALYSIS",
        html: render(
          VulnerabilityTemplate({ link: String(link._id), token: token })
        ),
      });
    });
  }
};

export const vendorAcceptance = async ({
  ctx,
  input,
}: ATC<typeof vendorAcceptanceValidator>) => {
  const {
    submissionId,
    vendorAcceptanceStatus,
    vendorRejectReason,
    rejectionEvidence,
  } = input;

  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission) {
    throw new TRPCError({ code: "NOT_FOUND", message: "submission not found" });
  }

  submission.vendorAcceptanceStatus = vendorAcceptanceStatus;
  submission.vendorRejectReason = vendorRejectReason;
  submission.vendorAcceptanceOrRejectionDate = new Date();
  if (rejectionEvidence) submission.rejectionEvidence = rejectionEvidence;

  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );

  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor)
    throw new TRPCError({ code: "NOT_FOUND", message: "vendor not found" });

  const users = await UserModel.find({
    companies: {
      // @ts-ignore
      $in: [vendor._id, vendor.reportsTo, submission.vulnerability.analysisBy],
    },
    role: {
      $in: [
        USER_ROLE.VENDOR,
        USER_ROLE.COMPANY,
        USER_ROLE.LEAD_SOC_ANALYST,
        USER_ROLE.SOC_ANALYST,
        USER_ROLE.EMPLOYEE,
      ],
    },
  }).lean();

  const reqUser = users.map((_) => _.role);

  const emails = users.map(($) => $.email);
  // create a expirable link
  const link = await new LinkModel({
    expiryDate: addDays(new Date(), 15),
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();

  // send emails
  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      await sendEmail({
        to: email,
        subject: "VENDOR RESPONDENT ACCEPTANCE STATUS",
        html: render(
          VulnerabilityAcceptanceMail({
            link: String(link._id),
            token: token,
            vulnerabilityAccceptanceDescription: input.vendorRejectReason,
            vulnerabilityAcceptanceStatus:
              input.vendorAcceptanceStatus === VendorAcceptanceStatus.ACCEPT
                ? "ACCEPTED"
                : input.vendorAcceptanceStatus === VendorAcceptanceStatus.REJECT
                ? "REJECTED"
                : "",
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission?.submission?.answer,
            vulnerability: submission?.vulnerability?.remarks,
          })
        ),
      });
    });
  }

  // create history for acceptance
  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `${vendorAcceptanceStatus}`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  }).save();

  return submission.save();
};

/** Vulnerability Resolve */
export const updateVulnerabilityResolveStatusByEmployee = async ({
  input: {
    resolveStatus,
    submissionId,
    vulnerabilityResolvedOrExceptionEvidence,
    resolveDescription,
  },
  ctx,
}: ATC<typeof updateVulnerabilityResolveStatusByEmployeeValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission) throw new TRPCError({ code: "NOT_FOUND" });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    resolveDescription,
    resolveStatus,
    vulnerabilityResolvedOrExceptionEvidence,
    resolvedDate: new Date(),
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );

  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();

  // save history
  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `Vulnerability resolved - ${resolveStatus}`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  }).save();

  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor || vendor.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "vendor not found in database",
    });

  // Send mail to vendor, company and soc
  const users = await UserModel.find({
    companies: {
      $in: [
        submission.vendor,
        submission.vulnerability.analysisBy,
        vendor.reportsTo,
      ],
    },
    role: {
      $in: [
        USER_ROLE.VENDOR,
        USER_ROLE.LEAD_SOC_ANALYST,
        USER_ROLE.SOC_ANALYST,
      ],
    },
  }).lean();

  const emails = users.map(($) => $.email);

  await submission.save();

  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      await sendEmail({
        to: email,
        subject: `VENDOR RESPONDENT VULNERABILITY ${resolveStatus.toUpperCase()} `,
        html: render(
          VulnerabilityResolveExceptionMail({
            link: String(link._id),
            token: token,
            vulnerability: submission?.vulnerability?.remarks,
            vulnerabilityResolveOrExceptionDescription: resolveDescription,
            vulnerabilityResolveOrExceptionStatus: resolveStatus,
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission.submission?.answer,
          })
        ),
      });
    });
  }
};

export const requestVulnerabilityExceptionSocToCompany = async ({
  input: {
    submissionId,
    vulnerabilityExceptionApprovalDescriptionBySoc,
    vulnerabilityExceptionApprovalEvidenceBySoc,
  },
  ctx,
}: ATC<typeof requestVulnerabilityExceptionSocToCompanyValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission) throw new TRPCError({ code: "NOT_FOUND" });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    ...(vulnerabilityExceptionApprovalDescriptionBySoc && {
      vulnerabilityExceptionApprovalDescriptionBySoc,
    }),
    ...(vulnerabilityExceptionApprovalEvidenceBySoc && {
      vulnerabilityExceptionApprovalEvidenceBySoc,
    }),
    companyExceptionApproval: CompanyExceptionApproval.PENDING,
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );
  await submission.save();

  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor || vendor.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "vendor not found in database",
    });

  //TODO: __HISTORY
  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: vendor.reportsTo,
    message: `Vulnerability exception request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });

  // Send emails to company users

  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();
  const companyUsers = await UserModel.find({
    companies: vendor.reportsTo,
    role: {
      $in: [
        USER_ROLE.COMPANY,
        USER_ROLE.LEAD_SOC_ANALYST,
        USER_ROLE.SOC_ANALYST,
      ],
    },
  });

  if (companyUsers.length) {
    companyUsers.forEach(async (user) => {
      const token = await createJwtByUserEmail(user?.email as string);
      sendEmail({
        to: user.email,
        subject: "VSOC VENDOR VULNERABILITY EXCEPTION REQUEST",
        html: render(
          VulnerabilityExceptionApprovalMail({
            link: String(link._id),
            exceptionDescription:
              vulnerabilityExceptionApprovalDescriptionBySoc,
            token: token,
            exception: submission.vulnerability?.resolveStatus?.toUpperCase(),
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission.submission?.answer,
            vendorExceptionDescription:
              submission?.vulnerability?.resolveDescription,
            vulnerability: submission?.vulnerability?.remarks,
          })
        ),
      });
    });
  }
};

export const requestVulnerabilityExceptionCompanyToSocApproval = async ({
  input: {
    companyExceptionApproval,
    submissionId,
    companyExceptionApprovalDescripton,
    companyExceptionApprovalEvidence,
    companyExceptionEndDate,
  },
  ctx,
}: ATC<typeof requestVulnerabilityExceptionCompanyToSocApprovalValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission)
    throw new TRPCError({
      code: "METHOD_NOT_SUPPORTED",
      message: "Submisson not found",
    });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    companyExceptionApproval,
    companyExceptionApprovalDescripton,
    companyExceptionApprovalEvidence,
    companyExceptionEndDate,
    companyExceptionApprovedDate: new Date(),
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );
  await submission.save();

  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `Vulnerability exception request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });

  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();

  // Send mail soc
  const soc = await UserModel.findOne({
    companies: submission.vendor,
    role: {
      $in: [USER_ROLE.SOC_ANALYST, USER_ROLE.COMPANY],
    },
  });

  if (!soc)
    throw new TRPCError({ code: "NOT_FOUND", message: "soc not found" });
  const token = await createJwtByUserEmail(soc?.email as string);
  sendEmail({
    to: soc?.email,
    subject: "VSOC VENDOR VULNERABILITY EXCEPTION STATUS FROM CUSTOMER",
    html: render(
      VulnerabilityExceptionApprovalStatusByCompanyMail({
        link: String(link._id),
        exceptionApprovalOrRejectDescription: companyExceptionApproval,
        exceptionApprovalOrRejectStatus: companyExceptionApprovalDescripton,
        token: token,
        vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
        vulnerabilityQuestion: submission?.submission?.answer,
        vulnerability: submission?.vulnerability?.remarks,
      })
    ),
  });
};
export const requestVulnerabilityExceptionSocToVendorApproved = async ({
  input: {
    submissionId,
    vulnerabilityExceptionStatusDateSocToVendor,
    vulnerabilityExceptionStatusSocToVendor,
  },
  ctx,
}: ATC<typeof approvedVulnerabilityExceptionSocToVendorValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission)
    throw new TRPCError({
      code: "METHOD_NOT_SUPPORTED",
      message: "Submisson not found",
    });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    vulnerabilityExceptionStatusDateSocToVendor: new Date(),
    vulnerabilityExceptionStatusSocToVendor,
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );

  const { companyExceptionApproval, companyExceptionApprovalDescripton } =
    submission.vulnerability;

  await submission.save();

  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `Vulnerability exception request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });
  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor || vendor.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "vendor not found in database",
    });
  const users = await UserModel.find({
    companies: { $in: [vendor.reportsTo, submission.vendor, submission.user] },
    role: {
      $in: [
        USER_ROLE.COMPANY,
        USER_ROLE.VENDOR,
        USER_ROLE.EMPLOYEE,
        USER_ROLE.SOC_ANALYST,
        USER_ROLE.LEAD_SOC_ANALYST,
      ],
    },
  });
  const emails = users.map(($) => $.email);
  const link = await new LinkModel({
    expiryDate: addDays(new Date(), 15),
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
      role: USER_ROLE.EMPLOYEE,
    },
  }).save();

  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      sendEmail({
        to: email,
        subject: " VENDOR VULNERABILITY EXCEPTION STATUS FROM CUSTOMER",
        html: render(
          VulnerabilityApprovedExceptionBycompanyToVendor({
            link: String(link._id),
            token: token,
            companyApproval: companyExceptionApproval,
            companyApprovalDescription: companyExceptionApprovalDescripton,
            socDescription: vulnerabilityExceptionStatusSocToVendor,
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission?.submission?.answer,
            vulnerability: submission?.vulnerability?.remarks,
          })
        ),
      });
    });
  }
};
/** Vulnerability Closure */
export const requestVulnerabilityClosureBySocToCompany = async ({
  input: {
    submissionId,
    vulnerabilityClosureDescriptionBySoc,
    vulnerabilityClosureEvidenceBySoc,
  },
  ctx,
}: ATC<typeof requestVulnerabilityClosureBySocToCompanyValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);

  if (!submission) throw new TRPCError({ code: "NOT_FOUND" });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    vulnerabilityClosureDescriptionBySoc,
    vulnerabilityClosureEvidenceBySoc,
    vulnerabilityClosureCompanyApprovalStatus:
      VulnerabilityClosureStatus.PENDING,
    vulnerabilityResolvedUpdateDateBySoc: new Date(),
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );

  await submission.save();

  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor || vendor.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "vendor not found in database",
    });

  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: vendor.reportsTo,
    message: `Vulnerability closure request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });

  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();

  const companyUsers = await UserModel.find({
    companies: vendor.reportsTo,
    role: {
      $in: [
        USER_ROLE.COMPANY,
        USER_ROLE.SOC_ANALYST,
        USER_ROLE.LEAD_SOC_ANALYST,
      ],
    },
  });
  if (companyUsers.length) {
    companyUsers.forEach(async (user) => {
      const token = await createJwtByUserEmail(user?.email as string);

      await sendEmail({
        to: user.email,
        subject: "VSOC VULNEABILITY CLOSURE REQUEST",
        html: render(
          VulnerabilityClosureApprovalSocToCompanyMail({
            link: String(link._id),
            closureDescription: vulnerabilityClosureDescriptionBySoc,
            token: token,
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission.submission?.answer,

            dateEvidenceFind: format(
              // @ts-ignore
              submission?.vulnerability?.createdAt,
              "MMM d,yyyy"
            ),
            vendorResolvedDescription:
              submission?.vulnerability?.resolveDescription,
            vulnerability: submission?.vulnerability?.remarks,
          })
        ),
      });
    });
  }
};

export const approveVulnerabilityClosureRequestByCompanyToSoc = async ({
  input: {
    submissionId,
    vulnerabilityClosureCompanyApprovalStatus,
    vulnerabilityClosureEvidenceByCompany,
    vulnerabilityClosureDescriptionByCompany,
  },
  ctx,
}: ATC<typeof approveVulnerabilityClosureRequestByCompanyToSocValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission) throw new TRPCError({ code: "NOT_FOUND" });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    vulnerabilityClosureCompanyApprovalStatus,
    vulnerabilityClosureEvidenceByCompany,
    vulnerabilityClosureDescriptionByCompany,
    vulnerabilityClosureDateByCompany: new Date(),
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );

  await submission.save();

  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `Vulnerability closure request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });

  const link = await new LinkModel({
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
    },
  }).save();

  const socUsers = await UserModel.findOne({
    companies: submission.vendor,
    role: {
      $in: [
        USER_ROLE.SOC_ANALYST,
        USER_ROLE.LEAD_SOC_ANALYST,
        USER_ROLE.COMPANY,
      ],
    },
  });

  // if (!socUsers)
  //   throw new TRPCError({ code: 'NOT_FOUND', message: 'soc not found' });
  const token = await createJwtByUserEmail(socUsers?.email as string);
  sendEmail({
    to: socUsers?.email,
    subject: "VSOC VULNERABILITY CLOSURE APPROVAL STATUS FROM CUSTOMER",
    html: render(
      VulnerabilityClosureApprovalStatusByCompanyMail({
        link: String(link._id),
        token: token,
        closureApprovalOrRejectDescription:
          vulnerabilityClosureDescriptionByCompany,
        closureApprovalOrRejectStatus:
          vulnerabilityClosureCompanyApprovalStatus.toUpperCase(),
        vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
        vulnerabilityQuestion: submission.submission?.answer,
        vulnerability: submission?.vulnerability?.remarks,
      })
    ),
  });
};

export const updateVulnerabilityClosureBySoc = async ({
  input: {
    submissionId,
    vulnerabilityClosureStatus,
    vulnerabilityClosedDescriptionBySoc,
  },
  ctx,
}: ATC<typeof updateVulnerabilityClosureBySocValidator>) => {
  const submission = await AssessmentSubmissionModel.findById(submissionId);
  if (!submission) throw new TRPCError({ code: "NOT_FOUND" });

  if (submission.sectionType !== SECTIONTYPE.INVENTORY) {
    throw new TRPCError({
      code: "CONFLICT",
      message: "not an inventory submission",
    });
  }

  submission.vulnerability = {
    ...submission.vulnerability,
    vulnerabilityClosureStatus,
    vulnerabilityClosedDescriptionBySoc,
    vulnerabilityClosureDateBySoc: new Date(),
  };
  const vulnerabilityQuestion = await QuestionModel.findById(
    submission.submission.question
  );
  await submission.save();

  await new AsessmentSubmissionHistoryModel({
    assessment: submission.assessment,
    actionTaker: ctx.user._id,
    actionReciver: submission.vulnerability.analysisBy,
    message: `Vulnerability closure request`,
    question: submission.submission.question,
    submission: submissionId,
    vendor: submission.vendor,
    submissionSnapshot: submission,
  });

  const vendor = await CompanyModel.findById(submission.vendor);
  if (!vendor || vendor.type !== CompanyType.VENDOR)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "vendor not found in database",
    });

  const users = await UserModel.find({
    companies: { $in: [vendor.reportsTo, submission.vendor, submission.user] },
    role: {
      $in: [
        USER_ROLE.COMPANY,
        USER_ROLE.VENDOR,
        USER_ROLE.EMPLOYEE,
        USER_ROLE.SOC_ANALYST,
        USER_ROLE.LEAD_SOC_ANALYST,
      ],
    },
  });

  const emails = users.map(($) => $.email);

  // create a expirable link
  const link = await new LinkModel({
    expiryDate: addDays(new Date(), 15),
    metadata: {
      type: "assessment",
      assessment: submission.assessment,
      questions: [submission.submission.question],
      role: USER_ROLE.EMPLOYEE,
    },
  }).save();

  // send emails
  if (emails.length) {
    emails.forEach(async (email) => {
      const token = await createJwtByUserEmail(email as string);
      await sendEmail({
        to: email,
        subject: "NOTIFICATION OF VULNERABILITY CLOSURE !",
        html: render(
          VulnerabilityClose({
            link: String(link._id),
            token: token,
            vulnerabilityClosed: vulnerabilityClosureStatus.toUpperCase(),
            vulnerabilityClosedDescription: vulnerabilityClosedDescriptionBySoc,
            vulnerabilityAnswer: vulnerabilityQuestion?.input?.label,
            vulnerabilityQuestion: submission.submission?.answer,
            vulnerability: submission?.vulnerability?.remarks,
          })
        ),
      });
    });
  }
};
