import { ATC } from "../@types/Trpc";
import {
  getNVDDataValidator,
  saveNVDDataValidator,
} from "../../shared/validators/save-nvd.validator";
import { CVSSSavingModel } from "../models/comparison-save.model";
import { TRPCError } from "@trpc/server";

export const saveNVDData = async ({
  input,
  ctx,
}: ATC<typeof saveNVDDataValidator>) => {
  try {
    console.log("Received input:", JSON.stringify(input, null, 2));

    // Validate that all required fields are present
    if (
      !input.assessment ||
      !input.vendor ||
      !input.company ||
      !input.cvssCalculator
    ) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message:
          "Missing required fields: assessment, vendor, company, or cvssCalculator",
      });
    }

    if (!input.nvdData) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "nvdData is required",
      });
    }

    if (!input.comments || input.comments.trim() === "") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Comments are required",
      });
    }

    const savedData = await CVSSSavingModel.create(input);
    console.log("Successfully saved:", savedData._id);

    return {
      success: true,
      data: savedData,
      message: "NVD comparison data saved successfully",
    };
  } catch (error) {
    console.error("Error saving NVD data:", error);

    if (error instanceof TRPCError) {
      throw error;
    }

    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to save NVD comparison data",
      cause: error,
    });
  }
};

export const getNVDData = async ({
  input,
}: ATC<typeof getNVDDataValidator>) => {
  try {
    const { assessment, vendor, company, asset } = input;

    const filter: any = {
      assessment,
      vendor,
      company,
      isVUlnerability: true,
      asset,
    };

    const data = await CVSSSavingModel.findOne(filter)
      .sort({ createdAt: -1 })
      .limit(10) // Limit to 10 most recent records
      .lean();

    return data;
  } catch (error) {
    console.error("Error fetching NVD data:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch NVD comparison data",
      cause: error,
    });
  }
};

export const getNVDAllData = async ({
  input,
}: ATC<typeof getNVDDataValidator>) => {
  const { assessment, vendor, company, asset } = input;

  const filter: any = {
    assessment,
    vendor,
    company,
    asset,
  };

  return await CVSSSavingModel.find(filter).lean();
};
