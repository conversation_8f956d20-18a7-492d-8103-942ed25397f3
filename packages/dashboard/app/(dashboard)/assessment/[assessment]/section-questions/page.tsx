"use client";
import FormBuilder from "@/components/form/FormBuilder";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { zodSchemaBuilder } from "@/lib/form";
import { cn, toastPromise } from "@/lib/utils";
import { trpc } from "@/providers/Providers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { ASSIGNMENT_STATUS } from "../../../../../../shared/types/Assesment";

import VendorAssessmentDetailsDatagrid from "@/elements/assessments/vendor-assessmentdetails-datagrid";
import { SECTIONTYPE } from "../../../../../../shared/types/Standard";
import { useRole } from "@/hooks/useRole";
import { getTotalGeneralQuestionScore } from "@/lib/submissions";

import QuestionFilePicker from "@/elements/assessments/question.picker";
import {
  ArrowBigRightDash,
  ArrowLeft,
  Check,
  CheckCheck,
  List,
} from "lucide-react";
import {
  ArrayParam,
  StringParam,
  useQueryParams,
  withDefault,
} from "use-query-params";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { Label } from "@/components/ui/label";

export default function EmployeeAssessmentDetails() {
  const { assessment } = useParams<{ assessment: string }>();
  const { data: session } = useSession();
  const { isEmployee } = useRole();

  const { data: assessmentDetails } = trpc.assessment.getAssessment.useQuery(
    {
      assessment,
    },
    {
      select(data) {
        if (isEmployee) {
          const assignments = (data?.assignments || []).filter(
            (assignment) => String(assignment.employee) === session?.user._id
          );

          return {
            assignments,
            vendor: String(data?.vendor),
            questions: assignments
              .map((assignment) =>
                (assignment.questions || []).map((q) => String(q._id))
              )
              .flat(),
          };
        } else {
          const assignments = data?.assignments || [];
          return {
            assignments,
            vendor: String(data?.vendor),
            questions: assignments
              .map((assignment) =>
                (assignment.questions || []).map((q) => String(q._id))
              )
              .flat(),
          };
        }
      },
    }
  );

  const questions = trpc.questions.getQuestions.useQuery(
    {
      questions: assessmentDetails?.questions as string[],
    },
    {
      enabled: !!assessmentDetails?.questions?.length,
      select(data) {
        const sortedQuestions: typeof data = [];
        const map: Record<string, (typeof data)[number]> = {};
        data.forEach((q) => {
          map[String(q._id)] = q;
        });

        assessmentDetails?.questions.forEach((qid) => {
          sortedQuestions.push(map[qid]);
        });
        return sortedQuestions;
      },
    }
  );

  const methods = useForm({
    resolver: zodResolver(zodSchemaBuilder(questions.data)),
    defaultValues: {},
  });

  const saveQuestionsState =
    trpc.assessment.updateQuestionsSubmissionState.useMutation();

  trpc.assessment.getUpdateQuestionsSubmissionState.useQuery(
    { assessment },
    {
      onSuccess(data) {
        if (data?.questionsState) {
          Object.entries(data.questionsState as Record<string, any>).map(
            ([key, data]) => {
              // @ts-ignore
              methods.setValue(key, data);
            }
          );
        }
      },
    }
  );

  const router = useRouter();

  const submitAssessment =
    trpc.assessment.createAssessmentSubmission.useMutation();

  const assessmentSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment },
      {
        select(data) {
          return {
            inventorySubmissions: data.filter(
              (d) =>
                d.sectionType === SECTIONTYPE.INVENTORY &&
                (isEmployee ? String(d.user) === session?.user._id : true)
            ),
            generalSubmissions: data.filter(
              (d) =>
                d.sectionType === SECTIONTYPE.GENERAL &&
                (isEmployee ? String(d.user) === session?.user._id : true)
            ),
            generalQuestionsScore: getTotalGeneralQuestionScore(data || []),
          };
        },
      }
    );

  const handleSubmit = methods.handleSubmit((data) => {
    const entry = Object.entries(data);

    toastPromise({
      asyncFunc: submitAssessment.mutateAsync(
        entry.map(([questionId, answer]) => ({
          assessment,
          vendor: String(assessmentDetails?.vendor),
          user: session?.user._id as string,
          submission: {
            question: questionId,
            answer: String(answer),
            // @ts-ignore
            files: methods.getValues(`file.${questionId}`) || [],
          },
        }))
      ),
      onSuccess() {
        router.replace("/");
      },
    });
  });

  const handleSaveProgress = () => {
    const questionIds = (questions.data || []).map((q) => String(q._id));
    const questionValues: Record<any, any> = {};

    questionIds.forEach((qid) => {
      // @ts-ignore
      questionValues[qid] = methods.getValues(qid);
    });

    toastPromise({
      asyncFunc: saveQuestionsState.mutateAsync({
        assessment,
        questionsState: questionValues,
      }),
      success: "progress saved successfully",
    });
  };

  const [searchParams, setSearchparams] = useQueryParams({
    selectedSection: withDefault(StringParam, ""),
    question: withDefault(ArrayParam, []),
  });

  const questionParams = searchParams.question as string[];
  const selectedSection = searchParams.selectedSection;

  const createQuestionFile = trpc.file.createQuestionFile.useMutation();

  const questionIds = useMemo(() => {
    return (questions.data || []).map((q) => String(q._id));
  }, [questions]);

  const filesByQuestion = trpc.file.getFiles.useQuery(
    { uploadedBy: session?.user._id, questions: questionIds },
    { enabled: !!questionIds.length }
  );

  const compliances = assessmentDetails?.assignments
    // @ts-expect-error
    ?.map((a) => a?.files)
    ?.flat()
    ?.map((_) => _?.compliance);

  return (
    <div className=" bg-white p-5 m-3 rounded-xl   ">
      {!assessmentDetails?.assignments.every(
        (assignment) =>
          assignment.assignmentStatus === ASSIGNMENT_STATUS.COMPLETED
      ) ? (
        <div className="flex gap-10 ">
          <div className="bg-cyan-400 p-5 rounded-xl w-[30vw]  shadow-lg shadow-black ">
            <Label className="text-3xl flex justify-center items-center uppercase  gap-4">
              <List size={30} />
              Sections
            </Label>
            <Separator className="bg-white p-[2px] my-5 rounded-xl" />
            {assessmentDetails?.assignments.map((as, idx) => (
              <div key={idx} className="flex flex-wrap flex-col ">
                {as.sections?.map((s, sectionIdx) => (
                  <div
                    key={sectionIdx}
                    className={cn("mr-4 mb-2 uppercase ", {
                      "text-xl bg-cyan-200 border rounded-lg shadow-lg scale-105 px-2 py-1":
                        selectedSection === s._id,
                    })}
                  >
                    <Link
                      href={`/assessment/${assessment}/section-questions?selectedSection=${s._id}`}
                      onClick={handleSaveProgress}
                    >
                      {s.section}
                    </Link>
                  </div>
                ))}
              </div>
            ))}
          </div>
          <form
            className="space-y-4  flex flex-col justify-center items-center  "
            onSubmit={handleSubmit}
          >
            <div className="  bg-gradient-to-tr from-red-200  to-yellow-200 shadow-md border-2 pr-10 drop-shadow-lg shadow-black rounded-lg w-[57vw]  ">
              <p className="text-2xl text-black font-bold text-center uppercase mt-5 ">
                {
                  assessmentDetails?.assignments
                    .find((as) =>
                      as.sections?.find((ss) => ss._id === selectedSection)
                    )
                    ?.sections?.find((sp) => sp._id === selectedSection)
                    ?.section
                }
                &nbsp;
              </p>
              <Separator className="bg-white p-[2px] my-5 rounded-xl ml-4  " />
              <FormProvider {...methods}>
                {(questions.data || [])
                  .filter((q) => {
                    const isCompliantQuestion = compliances?.some((c) =>
                      q.complience?.includes(c)
                    );

                    if (isCompliantQuestion) {
                      methods.setValue(
                        // @ts-expect-error
                        String(q._id),
                        "code:v-E42GGKAS3"
                      );
                      return false;
                    }

                    return selectedSection
                      ? selectedSection === String(q.section._id)
                      : true;
                  })
                  .map((q, idx) => {
                    const questionFileExists = filesByQuestion.data?.find(
                      (file) => file.question === String(q._id)
                    );

                    return (
                      <div
                        key={String(q._id)}
                        className="flex flex-col justify-center items-start  "
                      >
                        <div className="pl-10 flex flex-row items-start justify-center ">
                          <div className="mt-5 text-xl">{idx + 1})</div>
                          <FormBuilder
                            // @ts-ignore
                            question={q}
                            isCompliantQuestion
                          />
                        </div>

                        <div className="flex ">
                          {q.canAttachDocument ? (
                            !!questionFileExists ? (
                              <div className="flex flex-1 items-center gap-2 ">
                                <QuestionFilePicker
                                  onChange={({ base64, ext, mimeType }) => {
                                    toastPromise({
                                      asyncFunc: createQuestionFile.mutateAsync(
                                        {
                                          base64,
                                          ext,
                                          mimeType,
                                          name: q.input.label,
                                          question: String(q._id),
                                        }
                                      ),
                                      onSuccess(data) {
                                        questions.refetch();
                                        // TODO: Refetch questions + question file + what ever is required
                                      },
                                    });
                                  }}
                                />
                                <Check color="green" />
                              </div>
                            ) : (
                              <QuestionFilePicker
                                onChange={({ base64, ext, mimeType }) => {
                                  toastPromise({
                                    asyncFunc: createQuestionFile.mutateAsync({
                                      base64,
                                      ext,
                                      mimeType,
                                      name: q.input.label,
                                      question: String(q._id),
                                    }),
                                    onSuccess(data) {
                                      // TODO: Refetch questions + question file + what ever is required
                                    },
                                  });
                                }}
                              />
                            )
                          ) : null}
                        </div>
                      </div>
                    );
                  })}
              </FormProvider>
            </div>
            <Separator className="p-[2px] rounded-lg bg-sky-200 w-[50vw]" />
            <div className="flex gap-4 justify-center ">
              <Button
                type="button"
                size={"lg"}
                onClick={handleSaveProgress}
                className=" border-2 rounded-full hover:scale-110"
              >
                Save Progress
              </Button>
            </div>
          </form>
          <Button asChild>
            <Link href={`/assessment/${assessment}`} className=" gap-2">
              <ArrowLeft />
              Back
            </Link>
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <VendorAssessmentDetailsDatagrid
            type={SECTIONTYPE.INVENTORY}
            submissions={(
              assessmentSubmissions.data?.inventorySubmissions || []
            ).filter((sub) =>
              !questionParams.length
                ? true
                : questionParams.includes(String(sub._id))
            )}
          />
          {/* TODO: Dont remove | Auditor */}
          {/* <VendorAssessmentDetailsDatagrid
                type={SECTIONTYPE.GENERAL}
                submissions={(
                  assessmentSubmissions.data?.generalSubmissions || []
                ).filter((sub) =>
                  !questionParams.length
                    ? true
                    : questionParams.includes(
                        String(sub.submission.question?._id)
                      )
                )}
              /> */}
          {/* <div className='space-y-4'>
                <div>
                  <span className='font-semibold'>Questions Answered: </span>
                  {
                    assessmentSubmissions.data?.generalQuestionsScore
                      .totalAnsweredQuestions
                  }
                </div>
                <div>
                  <span className='font-semibold'>Score: </span>
                  {assessmentSubmissions.data?.generalQuestionsScore.scorePercent.toFixed(
                    2
                  )}
                  %
                </div>
              </div> */}
        </div>
      )}
    </div>
  );
}
