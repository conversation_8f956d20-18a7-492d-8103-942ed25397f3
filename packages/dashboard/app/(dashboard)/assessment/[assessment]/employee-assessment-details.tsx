"use client";
import FormBuilder from "@/components/form/FormBuilder";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { zodSchemaBuilder } from "@/lib/form";
import { cn, toastPromise } from "@/lib/utils";
import { trpc } from "@/providers/Providers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { ASSIGNMENT_STATUS } from "../../../../../shared/types/Assesment";

import VendorAssessmentDetailsDatagrid from "@/elements/assessments/vendor-assessmentdetails-datagrid";
import { SECTIONTYPE } from "../../../../../shared/types/Standard";
import { useRole } from "@/hooks/useRole";
import { getTotalGeneralQuestionScore } from "@/lib/submissions";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogHeader,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import QuestionFilePicker from "@/elements/assessments/question.picker";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { CheckCheck } from "lucide-react";
import Link from "next/link";
import toast from "react-hot-toast";
import { Separator } from "@/components/ui/separator";

export default function EmployeeAssessmentDetails() {
  const { assessment } = useParams<{ assessment: string }>();
  const { data: session } = useSession();
  const { isEmployee } = useRole();
  const searchParams = useSearchParams();
  const questionParams = searchParams.getAll("question");

  const { data: assessmentDetails } = trpc.assessment.getAssessment.useQuery(
    {
      assessment,
    },
    {
      select(data) {
        if (isEmployee) {
          const assignments = (data?.assignments || []).filter(
            (assignment) => String(assignment.employee) === session?.user._id
          );

          return {
            assignments,
            vendor: String(data?.vendor),
            questions: assignments
              .map((assignment) =>
                (assignment.questions || []).map((q) => String(q._id))
              )
              .flat(),
          };
        } else {
          const assignments = data?.assignments || [];
          return {
            assignments,
            vendor: String(data?.vendor),
            questions: assignments
              .map((assignment) =>
                (assignment.questions || []).map((q) => String(q._id))
              )
              .flat(),
          };
        }
      },
    }
  );

  const questions = trpc.questions.getQuestions.useQuery(
    {
      questions: assessmentDetails?.questions as string[],
    },
    { enabled: !!assessmentDetails?.questions?.length }
  );

  const methods = useForm({
    resolver: zodResolver(zodSchemaBuilder(questions.data)),
    defaultValues: {},
  });

  const saveQuestionsState =
    trpc.assessment.updateQuestionsSubmissionState.useMutation();

  trpc.assessment.getUpdateQuestionsSubmissionState.useQuery(
    { assessment },
    {
      onSuccess(data) {
        if (data?.questionsState) {
          Object.entries(data.questionsState as Record<string, any>).map(
            ([key, data]) => {
              // @ts-ignore
              methods.setValue(key, data);
            }
          );
        }
      },
    }
  );

  const router = useRouter();

  const submitAssessment =
    trpc.assessment.createAssessmentSubmission.useMutation();

  const assessmentSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment },
      {
        select(data) {
          return {
            inventorySubmissions: data.filter(
              (d) =>
                d.sectionType === SECTIONTYPE.INVENTORY &&
                (isEmployee ? String(d.user) === session?.user._id : true)
            ),
            generalSubmissions: data.filter(
              (d) =>
                d.sectionType === SECTIONTYPE.GENERAL &&
                (isEmployee ? String(d.user) === session?.user._id : true)
            ),
            generalQuestionsScore: getTotalGeneralQuestionScore(data || []),
          };
        },
      }
    );

  const errors = methods.formState.errors;

  const handleSubmit = () => {
    if (Object.keys(errors).length)
      return toast.error("Please fill all the required fields");
    methods.handleSubmit((data) => {
      const entry = Object.entries(data);

      toastPromise({
        asyncFunc: submitAssessment.mutateAsync(
          entry.map(([questionId, answer]) => ({
            assessment,
            vendor: String(assessmentDetails?.vendor),
            user: session?.user._id as string,
            submission: {
              question: questionId,
              answer: String(answer),
              // @ts-ignore
              files: methods.getValues(`file.${questionId}`) || [],
            },
          }))
        ),
        onSuccess() {
          router.replace("/");
        },
      });
    })();
  };

  const questionsData = methods.watch();

  // const _totalQuestions = (questions.data || [])
  //   .filter((q) => q.section._id)
  //   .map((q) => String(q._id));
  // const _questionsAnswered = Object.entries(questionsData).filter(
  //   ([questionId, answer]) => {
  //     return (
  //       _totalQuestions.includes(questionId) &&
  //       !!String(answer) &&
  //       answer !== null &&
  //       answer !== undefined
  //     );
  //   }
  // );

  // const totalSections = useMemo(() => {
  //   return assessmentDetails?.assignments.flatMap((s) => s.sections);
  // }, [assessmentDetails?.assignments]);
  const compliances = assessmentDetails?.assignments
    // @ts-expect-error
    ?.map((a) => a?.files)
    ?.flat()
    ?.map((_) => _?.compliance);

  const sectionData = assessmentDetails?.assignments
    .flatMap((assignment) =>
      assignment.sections?.map((section) => {
        const totalQuestions = compliances?.length
          ? (questions.data || [])
              .filter((q) => {
                const isCompliantQuestion = compliances?.some(
                  (c) => !q.complience?.includes(c)
                );

                if (isCompliantQuestion) return true;

                q.section._id === String(section._id);
              })
              .map((q) => String(q._id))
          : (questions.data || []).map((q) => String(q._id));

        const questionsAnswered = Object.entries(questionsData).filter(
          ([questionId, answer]) => {
            return (
              totalQuestions.includes(questionId) &&
              !!String(answer) &&
              answer !== null &&
              answer !== undefined
            );
          }
        );

        const questionsAttemptedPercentage =
          (questionsAnswered.length / totalQuestions.length) * 100;

        return {
          section_id: section._id,
          questionsAttemptedPercentage,
          totalQuestions,
          questionsAnswered,
          sectionlabel: section?.sectionLable,
          section: section.section,
        };
      })
    )
    .filter(Boolean);

  const _totalQuestions = sectionData?.flatMap((data) => data?.totalQuestions);
  const _questionsAnswered = sectionData?.flatMap(
    (data) => data?.questionsAnswered
  );
  const answeredQuestions = _questionsAnswered ? _questionsAnswered?.length : 0;
  const questionsTotal = _totalQuestions ? _totalQuestions?.length : 0;
  // Calculate total sections
  const totalSections = sectionData ? sectionData?.length : 0;

  // Calculate completed and pending sections
  const completedSections = sectionData?.filter(
    (data) => data?.questionsAttemptedPercentage === 100
  )
    ? sectionData?.filter((data) => data?.questionsAttemptedPercentage === 100)
        .length
    : 0;

  const pendingSections = totalSections - completedSections;

  return (
    <div className="flex flex-col gap-4 p-10 m-3 rounded-lg bg-gradient-to-br from-green-200 to-blue-500 ">
      {/* Submission History details */}

      <div className="flex justify-center items-center gap-2">
        <Card className=" bg-gradient-to-t from-indigo-400 to-cyan-400 h-[15vh] w-[30vw] border-2 border-cyan-100 rounded-full size-72 flex flex-col items-center justify-center">
          <CardTitle className=" mb-2 text-center">Total Questions</CardTitle>

          <CardContent className="text-sm">
            <div className="flex flex-row items-center justify-between  ">
              <p className=" font-medium text-base ">Total Assigned Question</p>
              :<p className=" font-semibold">{_totalQuestions?.length}</p>
            </div>
            <div className="flex flex-row items-center justify-between">
              <p className=" font-medium text-base ">Total Answered Question</p>
              :<p className=" font-semibold">{_questionsAnswered?.length}</p>
            </div>
            <div className="flex flex-row items-center justify-between">
              <p className=" font-medium text-base ">Total Pending Question</p>:
              <p className=" font-semibold">
                {-answeredQuestions + questionsTotal}
              </p>
            </div>
          </CardContent>
        </Card>
        <Separator
          orientation="vertical"
          className="p-[2px] rounded-full bg-slate-500 w-[3vw]"
        />
        <Card className=" bg-gradient-to-t   from-violet-200 to-pink-200 h-[15vh]  w-[30vw] border-2 border-cyan-100 rounded-full size-72 flex flex-col items-center justify-center">
          <CardTitle className=" mb-2 p-2 text-center">
            Total Sections
          </CardTitle>

          <CardContent className="flex flex-row items-center justify-between">
            <p className=" font-medium text-base  ">Total Assigned Sections</p>:
            <p className=" font-semibold">{totalSections}</p>
          </CardContent>
        </Card>
        <Separator
          orientation="vertical"
          className="p-[2px] rounded-full bg-slate-500 w-[3vw]"
        />
        <Card className=" bg-gradient-to-b  from-teal-200  to-teal-500 h-[15vh]  w-[30vw] border-2 border-cyan-100 rounded-full size-72 flex flex-col items-center justify-center ">
          <CardTitle className=" mb-2 p-2 text-center">
            Completed Sections
          </CardTitle>
          <CardContent className="text-sm">
            <div className="flex flex-row items-center justify-between  ">
              <p className=" font-medium text-base ">
                Total completed sections
              </p>
              :<p className=" font-semibold">{completedSections}</p>
            </div>
            <div className="flex flex-row items-center justify-between">
              <p className=" font-medium text-base ">
                Total incompleted sections
              </p>
              :<p className=" font-semibold">{pendingSections}</p>
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="col-span-3  ">
        <Table className=" ">
          <TableHeader className="bg-teal-400 rounded-xl">
            <TableHead>Section</TableHead>
            <TableHead className="text-center w-[1000px]">Status</TableHead>
            <TableHead className="text-center w-[300px]">Action</TableHead>
          </TableHeader>

          <TableBody>
            {sectionData?.map((sec) => {
              const totalQuestionsBySection = sec?.totalQuestions?.length;
              const answeredQuestionsBySection = sec?.questionsAnswered?.length;
              return (
                <TableRow key={sec?.section_id}>
                  <TableCell className="font-semibold text-xl">
                    {sec?.section}
                  </TableCell>
                  <TableCell className="flex items-center gap-2 mt-2 ">
                    <Progress
                      value={sec?.questionsAttemptedPercentage}
                      className="h-[8px] "
                    />
                    <div className="flex flex-row gap-1 font-semibold">
                      <p>{answeredQuestionsBySection}</p> /
                      <p>{totalQuestionsBySection}</p>
                    </div>
                  </TableCell>
                  <TableCell className=" text-center  ">
                    <Button
                      asChild
                      className={cn(
                        `${
                          sec?.questionsAttemptedPercentage === 100
                            ? "bg-red-500 px-2"
                            : sec?.questionsAttemptedPercentage === 0
                            ? "bg-primary"
                            : "bg-amber-400"
                        }`
                      )}
                    >
                      <Link
                        href={`/assessment/${assessment}/section-questions?selectedSection=${String(
                          sec?.section_id
                        )}`}
                      >
                        {sec?.questionsAttemptedPercentage === 100
                          ? "Completed/Edit"
                          : sec?.questionsAttemptedPercentage === 0
                          ? "Start"
                          : "Continue"}
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell colSpan={1}></TableCell>
              <TableCell colSpan={1} className="text-end">
                Submit Assessment : &nbsp;
              </TableCell>
              <TableCell className="text-center">
                <Button onClick={handleSubmit}> Submit</Button>
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>
    </div>
  );
}
