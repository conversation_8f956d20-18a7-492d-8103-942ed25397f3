"use client";

import { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { trpc } from "@/providers/Providers";
import { toastPromise } from "@/lib/utils";
import { useSession } from "next-auth/react";
import { Toaster } from "react-hot-toast";
import { useQueryParams } from "use-query-params";
import { motion } from "framer-motion";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { LucideSaveAll } from "lucide-react";

interface CVSSMetrics {
  AV: string; // Attack Vector
  AC: string; // Attack Complexity
  AT: string; // Attack Requirements
  PR: string; // Privileges Required
  UI: string; // User Interaction
  MC: string; // Modified Confidentiality
  MI: string; // Modified Integrity
  MA: string; // Modified Availability
  S: string; // Safety
  R: string; // Recovery
  AU: string; // Automation
}

interface RiskInputs {
  baseScore: number;
  exploitAvailable: boolean;
  exploitedInWild: boolean;
  assetCriticality: number;
  businessImpact: number;
  exposureLevel: number;
  mitigationStrength: number;
}

interface RiskResults {
  baseScore: number;
  envScoreBefore: number;
  envScoreAfter: number;
  deltaScore: number;
  riskLevelBefore: string;
  riskLevelAfter: string;
  likelihoodBefore: number;
  likelihoodAfter: number;
  impactBefore: number;
  impactAfter: number;
  residualVector: string;
}

interface MitigationControl {
  name: string;
  checked: boolean;
}

interface MitigationControls {
  Network: MitigationControl[];
  Application: MitigationControl[];
  Database: MitigationControl[];
  Encryption: MitigationControl[];
}

export default function Calculator() {
  const params = useParams();
  const para = useSearchParams();
  const company = para.get("company");
  const vendor = para.get("vendor");
  const { data: session } = useSession();
  const assessment = params.assessment as string;

  const [cvssMetrics, setCvssMetrics] = useState<CVSSMetrics>({
    AV: "",
    AC: "",
    AT: "",
    PR: "",
    UI: "",
    MC: "",
    MI: "",
    MA: "",
    S: "",
    R: "",
    AU: "",
  });

  const [riskInputs, setRiskInputs] = useState<RiskInputs>({
    baseScore: 7.5,
    exploitAvailable: false,
    exploitedInWild: false,
    assetCriticality: 5,
    businessImpact: 5,
    exposureLevel: 5,
    mitigationStrength: 0,
  });

  const [cvssVector, setCvssVector] = useState<string>("CVSS:4.0");
  const [riskResults, setRiskResults] = useState<RiskResults | null>(null);
  const [securityRiskOverview, setSecurityRiskOverview] = useState<string>("");
  const [vendorAIScore, setVendorAIScore] = useState<string>("");
  const [showMitigationPanel, setShowMitigationPanel] =
    useState<boolean>(false);
  const [calculationName, setCalculationName] = useState<string>("");
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // TRPC mutations
  const saveCalculatorData = trpc.calculator.saveCalculatorData.useMutation();
  const getCalculatorData = trpc.calculator.getCalculatorData.useQuery(
    { assessment },
    { enabled: !!assessment }
  );
  const [mitigationControls, setMitigationControls] =
    useState<MitigationControls>({
      Network: [
        { name: "Firewall", checked: false },
        { name: "Load Balancer", checked: false },
        { name: "IDS/IPS", checked: false },
        { name: "DMZ", checked: false },
        { name: "Access Control / NAC", checked: false },
        { name: "Other", checked: false },
      ],
      Application: [
        { name: "WAF", checked: false },
        { name: "MFA", checked: false },
        { name: "OAuth2", checked: false },
        { name: "Input Validation & Sanitization", checked: false },
        { name: "Secure Session Management", checked: false },
        { name: "TLS/HTTPS Encryption", checked: false },
        { name: "Other", checked: false },
      ],
      Database: [
        { name: "Authentication & Access Control", checked: false },
        { name: "Encryption", checked: false },
        { name: "Input Validation & Query Protection", checked: false },
        { name: "Database Activity Monitoring (DAM)", checked: false },
        { name: "Other", checked: false },
      ],
      Encryption: [
        { name: "Data-at-rest Encryption", checked: false },
        { name: "Browser Encryption Acceptance", checked: false },
        { name: "Other", checked: false },
      ],
    });

  // Build CVSS v4.0 Vector
  const buildV40Vector = () => {
    let vector = "CVSS:4.0";
    Object.entries(cvssMetrics).forEach(([, value]) => {
      if (value) {
        vector += "/" + value;
      }
    });
    setCvssVector(vector);

    // Extract base score from vector (simplified logic from HTML)
    const baseScore =
      vector.includes("MC:H") ||
      vector.includes("MI:H") ||
      vector.includes("MA:H")
        ? 9.8
        : 7.5;
    setRiskInputs((prev) => ({ ...prev, baseScore }));
  };

  // Get risk level from score
  const getRiskLevel = (score: number): string => {
    if (score <= 0.2) return "Low";
    if (score <= 0.4) return "Moderate";
    if (score <= 0.7) return "High";
    return "Very High";
  };

  // Calculate mitigation strength from dynamic controls
  const getMitigationStrength = (): number => {
    const categories = [
      "Network",
      "Application",
      "Database",
      "Encryption",
    ] as const;
    let totalMitigation = 0;

    categories.forEach((category) => {
      const controls = mitigationControls[category];
      const totalControls = controls.length;
      if (totalControls === 0) return;

      const checkedControls = controls.filter(
        (control) => control.checked
      ).length;
      const categoryMitigation = 0.25 * (checkedControls / totalControls);
      totalMitigation += categoryMitigation;
    });

    return totalMitigation;
  };

  // Update mitigation control
  const updateMitigationControl = (
    category: keyof MitigationControls,
    index: number,
    checked: boolean
  ) => {
    setMitigationControls((prev) => ({
      ...prev,
      [category]: prev[category].map((control, i) =>
        i === index ? { ...control, checked } : control
      ),
    }));
  };

  // Add new mitigation control
  const addMitigationControl = (
    category: keyof MitigationControls,
    name: string
  ) => {
    setMitigationControls((prev) => ({
      ...prev,
      [category]: [...prev[category], { name, checked: false }],
    }));
  };

  // Remove mitigation control
  const removeMitigationControl = (
    category: keyof MitigationControls,
    index: number
  ) => {
    setMitigationControls((prev) => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index),
    }));
  };

  // Calculate environmental score with mitigation
  const calcEnvScore = (mitigation: number) => {
    const {
      baseScore,
      exploitAvailable,
      exploitedInWild,
      exposureLevel,
      assetCriticality,
      businessImpact,
    } = riskInputs;

    const exploit = exploitAvailable ? 1 : 0.5;
    const wild = exploitedInWild ? 1 : 0.5;

    const like = (baseScore / 10 + exploit + wild + exposureLevel / 5) / 4;
    const imp = (assetCriticality + businessImpact) / 10;
    const likeMit = like * (1 - mitigation);
    const impMit = imp * (1 - mitigation * 0.8);

    return {
      score: likeMit * impMit,
      like: likeMit,
      imp: impMit,
      likeRaw: like,
      impRaw: imp,
    };
  };

  // Save calculator data to MongoDB
  const saveCalculation = async () => {
    if (!riskResults) {
      alert("Please calculate risk first before saving.");
      return;
    }

    if (!assessment) {
      alert("Assessment ID is required to save calculation.");
      return;
    }

    // Get vendor ID from assessment data or use first company from session

    setIsSaving(true);

    const calculationData = {
      assessment,
      vendor: vendor ?? "",
      company: company ?? "",
      cvssMetrics,
      riskInputs: {
        ...riskInputs,
        mitigationStrength:
          getMitigationStrength() > 0
            ? getMitigationStrength()
            : riskInputs.mitigationStrength,
      },
      mitigationControls,
      cvssVector,
      riskResults,
      securityRiskOverview,
      vendorAIScore,
      name:
        calculationName ||
        `CVSS Calculation - ${new Date().toLocaleDateString()}`,
    };

    toastPromise({
      asyncFunc: saveCalculatorData.mutateAsync(calculationData),
      success: "Calculator data saved successfully!",
      error: "Failed to save calculator data",
      onSuccess: () => {
        getCalculatorData.refetch();
        setCalculationName("");
      },
      onError: (error) => {
        console.error("Save error:", error);
      },
    });

    setIsSaving(false);
  };

  // Main risk calculation function
  const calculateRisk = () => {
    if (isNaN(riskInputs.baseScore)) {
      alert("Please enter or generate a valid CVSS vector and base score.");
      return;
    }

    // Use dynamic mitigation controls if any are checked, otherwise use slider
    const dynamicMitigation = getMitigationStrength();
    const finalMitigation =
      dynamicMitigation > 0 ? dynamicMitigation : riskInputs.mitigationStrength;

    const envBefore = calcEnvScore(0.0);
    const envAfter = calcEnvScore(finalMitigation);

    const results: RiskResults = {
      baseScore: riskInputs.baseScore,
      envScoreBefore: envBefore.score,
      envScoreAfter: envAfter.score,
      deltaScore: envBefore.score - envAfter.score,
      riskLevelBefore: getRiskLevel(envBefore.score),
      riskLevelAfter: getRiskLevel(envAfter.score),
      likelihoodBefore: envBefore.like,
      likelihoodAfter: envAfter.like,
      impactBefore: envBefore.imp,
      impactAfter: envAfter.imp,
      residualVector: `${cvssVector} + MIT:${Math.round(
        finalMitigation * 100
      )}% | RES:${envAfter.score.toFixed(2)} | LEVEL:${getRiskLevel(
        envAfter.score
      )}`,
    };

    setRiskResults(results);
  };

  // Update CVSS metric
  const updateCVSSMetric = (key: keyof CVSSMetrics, value: string) => {
    setCvssMetrics((prev) => ({ ...prev, [key]: value }));
  };

  // Update risk input
  const updateRiskInput = (key: keyof RiskInputs, value: number | boolean) => {
    setRiskInputs((prev) => ({ ...prev, [key]: value }));
  };

  // Set mitigation preset
  const setMitigationPreset = (value: number) => {
    setRiskInputs((prev) => ({ ...prev, mitigationStrength: value }));
  };

  // Auto-build vector when metrics change
  useEffect(() => {
    buildV40Vector();
  }, [cvssMetrics]);

  return (
    <>
      <Toaster position="top-right" />
      <motion.h3
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="text-2xl lg:text-3xl font-extrabold text-center mb-8 
             text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500 
             drop-shadow-[0_1px_15px_rgba(0,255,255,0.2)]"
      >
        Advanced VendorAI CVSS v4.0 Threat Modeling & Risk Scoring Engine
      </motion.h3>
      <div className="px-24  space-y-6">
        {/* Saved Calculations Section */}
        <Accordion type="single" collapsible>
          <AccordionItem value="1">
            <AccordionTrigger>
              <LucideSaveAll className="w-4 h-4 mr-2" />
              Saved Calculations for this Assessment
            </AccordionTrigger>
            <AccordionContent>
              {getCalculatorData.data &&
                getCalculatorData.data.data.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-blue-600">
                        📋 Saved Calculations for this Assessment
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {getCalculatorData.data.data.map(
                          (calc: any, index: number) => (
                            <div
                              key={calc._id}
                              className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                              onClick={() => {
                                // Load saved calculation data
                                setCvssMetrics(calc.cvssMetrics);
                                setRiskInputs(calc.riskInputs);
                                setMitigationControls(calc.mitigationControls);
                                setCvssVector(calc.cvssVector);
                                setRiskResults(calc.riskResults);
                                setSecurityRiskOverview(
                                  calc.securityRiskOverview
                                );
                                setVendorAIScore(calc.vendorAIScore);
                              }}
                            >
                              <h4 className="font-semibold text-sm">
                                {calc.name || `Calculation ${index + 1}`}
                              </h4>
                              <p className="text-xs text-gray-500">
                                {new Date(
                                  calc.calculationDate
                                ).toLocaleDateString()}
                              </p>
                              <p className="text-xs text-gray-600">
                                Base Score: {calc.riskResults.baseScore} | Risk:{" "}
                                {calc.riskResults.riskLevelAfter}
                              </p>
                            </div>
                          )
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* CVSS v4.0 Vector Builder */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">
              🧱 CVSS v4.0 Vendor Environment Vector Builder
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="av-select" className="text-blue-600">
                  Attack Vector (AV)
                </Label>
                <Select
                  value={cvssMetrics.AV}
                  onValueChange={(value) => updateCVSSMetric("AV", value)}
                >
                  <SelectTrigger id="av-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="AV:N">Network</SelectItem>
                    <SelectItem value="AV:A">Adjacent</SelectItem>
                    <SelectItem value="AV:L">Local</SelectItem>
                    <SelectItem value="AV:P">Physical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ac-select" className="text-blue-600">
                  Attack Complexity (AC)
                </Label>
                <Select
                  value={cvssMetrics.AC}
                  onValueChange={(value) => updateCVSSMetric("AC", value)}
                >
                  <SelectTrigger id="ac-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="AC:L">Low</SelectItem>
                    <SelectItem value="AC:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="at-select" className="text-blue-600">
                  Attack Requirements (AT)
                </Label>
                <Select
                  value={cvssMetrics.AT}
                  onValueChange={(value) => updateCVSSMetric("AT", value)}
                >
                  <SelectTrigger id="at-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="AT:N">None</SelectItem>
                    <SelectItem value="AT:P">Present</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="pr-select" className="text-blue-600">
                  Privileges Required (PR)
                </Label>
                <Select
                  value={cvssMetrics.PR}
                  onValueChange={(value) => updateCVSSMetric("PR", value)}
                >
                  <SelectTrigger id="pr-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="PR:N">None</SelectItem>
                    <SelectItem value="PR:L">Low</SelectItem>
                    <SelectItem value="PR:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ui-select" className="text-blue-600">
                  User Interaction (UI)
                </Label>
                <Select
                  value={cvssMetrics.UI}
                  onValueChange={(value) => updateCVSSMetric("UI", value)}
                >
                  <SelectTrigger id="ui-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="UI:N">None</SelectItem>
                    <SelectItem value="UI:R">Required</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <h3 className="text-lg font-semibold mb-4 text-blue-600 mt-6">
              🌍 Environmental Metrics
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="mc-select" className="text-blue-600">
                  Modified Confidentiality (MC)
                </Label>
                <Select
                  value={cvssMetrics.MC}
                  onValueChange={(value) => updateCVSSMetric("MC", value)}
                >
                  <SelectTrigger id="mc-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="MC:N">None</SelectItem>
                    <SelectItem value="MC:L">Low</SelectItem>
                    <SelectItem value="MC:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="mi-select" className="text-blue-600">
                  Modified Integrity (MI)
                </Label>
                <Select
                  value={cvssMetrics.MI}
                  onValueChange={(value) => updateCVSSMetric("MI", value)}
                >
                  <SelectTrigger id="mi-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="MI:N">None</SelectItem>
                    <SelectItem value="MI:L">Low</SelectItem>
                    <SelectItem value="MI:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ma-select" className="text-blue-600">
                  Modified Availability (MA)
                </Label>
                <Select
                  value={cvssMetrics.MA}
                  onValueChange={(value) => updateCVSSMetric("MA", value)}
                >
                  <SelectTrigger id="ma-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="MA:N">None</SelectItem>
                    <SelectItem value="MA:L">Low</SelectItem>
                    <SelectItem value="MA:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <h3 className="text-lg font-semibold mb-4 text-blue-600 mt-6">
              🔧 Supplemental Metrics
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="s-select" className="text-blue-600">
                  Safety (S)
                </Label>
                <Select
                  value={cvssMetrics.S}
                  onValueChange={(value) => updateCVSSMetric("S", value)}
                >
                  <SelectTrigger id="s-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="S:N">None</SelectItem>
                    <SelectItem value="S:L">Low</SelectItem>
                    <SelectItem value="S:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="r-select" className="text-blue-600">
                  Recovery (R)
                </Label>
                <Select
                  value={cvssMetrics.R}
                  onValueChange={(value) => updateCVSSMetric("R", value)}
                >
                  <SelectTrigger id="r-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="R:N">None</SelectItem>
                    <SelectItem value="R:L">Low</SelectItem>
                    <SelectItem value="R:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="au-select" className="text-blue-600">
                  Automation (AU)
                </Label>
                <Select
                  value={cvssMetrics.AU}
                  onValueChange={(value) => updateCVSSMetric("AU", value)}
                >
                  <SelectTrigger id="au-select">
                    <SelectValue placeholder="--" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=" ">--</SelectItem>
                    <SelectItem value="AU:N">None</SelectItem>
                    <SelectItem value="AU:L">Low</SelectItem>
                    <SelectItem value="AU:H">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2 mt-6">
              <Label htmlFor="cvss-vector" className="text-blue-600">
                Generated CVSS Vector
              </Label>
              <Input
                id="cvss-vector"
                type="text"
                className="font-mono bg-gray-50"
                value={cvssVector}
                readOnly
              />
            </div>

            <div className="text-center mt-6">
              <Button
                type="button"
                onClick={buildV40Vector}
                className="bg-blue-600 hover:bg-blue-700"
              >
                🔄 Generate Vector
              </Button>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-blue-600">
                🛡️ Dynamic Mitigation Controls (Optional)
              </CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowMitigationPanel(!showMitigationPanel)}
                className="text-blue-600"
              >
                {showMitigationPanel ? "▲" : "▼"}
              </Button>
            </div>
          </CardHeader>
          {showMitigationPanel && (
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Network Controls */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-blue-600 font-semibold">
                      Network Controls
                      <span
                        className="text-xs text-gray-500 ml-1"
                        title="Layered protections like Firewall, IDS/IPS, Load Balancer, DMZ, NAC, etc."
                      >
                        ?
                      </span>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const name = prompt("Enter Network control name:");
                        if (name) addMitigationControl("Network", name);
                      }}
                    >
                      + Add
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {mitigationControls.Network.map((control, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`network-${index}`}
                            checked={control.checked}
                            onCheckedChange={(checked) =>
                              updateMitigationControl(
                                "Network",
                                index,
                                checked as boolean
                              )
                            }
                          />
                          <Label
                            htmlFor={`network-${index}`}
                            className="text-sm"
                          >
                            {control.name}
                          </Label>
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            removeMitigationControl("Network", index)
                          }
                          className="h-6 w-6 p-0"
                        >
                          ✕
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Application Controls */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-blue-600 font-semibold">
                      Application Controls
                      <span
                        className="text-xs text-gray-500 ml-1"
                        title="App protections like WAF, MFA, OAuth2, input validation, session management, TLS/HTTPS."
                      >
                        ?
                      </span>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const name = prompt("Enter Application control name:");
                        if (name) addMitigationControl("Application", name);
                      }}
                    >
                      + Add
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {mitigationControls.Application.map((control, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`app-${index}`}
                            checked={control.checked}
                            onCheckedChange={(checked) =>
                              updateMitigationControl(
                                "Application",
                                index,
                                checked as boolean
                              )
                            }
                          />
                          <Label htmlFor={`app-${index}`} className="text-sm">
                            {control.name}
                          </Label>
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            removeMitigationControl("Application", index)
                          }
                          className="h-6 w-6 p-0"
                        >
                          ✕
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Database Controls */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-blue-600 font-semibold">
                      Database Controls
                      <span
                        className="text-xs text-gray-500 ml-1"
                        title="DB controls like authentication, encryption, input validation, DAM."
                      >
                        ?
                      </span>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const name = prompt("Enter Database control name:");
                        if (name) addMitigationControl("Database", name);
                      }}
                    >
                      + Add
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {mitigationControls.Database.map((control, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`db-${index}`}
                            checked={control.checked}
                            onCheckedChange={(checked) =>
                              updateMitigationControl(
                                "Database",
                                index,
                                checked as boolean
                              )
                            }
                          />
                          <Label htmlFor={`db-${index}`} className="text-sm">
                            {control.name}
                          </Label>
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            removeMitigationControl("Database", index)
                          }
                          className="h-6 w-6 p-0"
                        >
                          ✕
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Encryption Controls */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-blue-600 font-semibold">
                      Encryption Controls
                      <span
                        className="text-xs text-gray-500 ml-1"
                        title="Data-at-rest, browser encryption, protocol enforcement, etc."
                      >
                        ?
                      </span>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const name = prompt("Enter Encryption control name:");
                        if (name) addMitigationControl("Encryption", name);
                      }}
                    >
                      + Add
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {mitigationControls.Encryption.map((control, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`enc-${index}`}
                            checked={control.checked}
                            onCheckedChange={(checked) =>
                              updateMitigationControl(
                                "Encryption",
                                index,
                                checked as boolean
                              )
                            }
                          />
                          <Label htmlFor={`enc-${index}`} className="text-sm">
                            {control.name}
                          </Label>
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            removeMitigationControl("Encryption", index)
                          }
                          className="h-6 w-6 p-0"
                        >
                          ✕
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <Label className="text-blue-600 font-semibold">
                    Mitigation Strength:{" "}
                    {Math.round(getMitigationStrength() * 100)}%
                  </Label>
                  <span
                    className="text-xs text-gray-500"
                    title="Each category can contribute up to 25%. The more sub-controls checked, the more mitigation you get."
                  >
                    ?
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  <strong>Note:</strong> This panel does not interfere with your
                  mitigation slider. Use either or both for analysis.
                </p>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Risk Assessment Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">
              ⚙️ Attack Surface Assessment: Risk Inputs and Defensive Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="base-score" className="text-blue-600">
                  Base Score (Auto from Vector)
                </Label>
                <Input
                  id="base-score"
                  type="number"
                  className="bg-gray-50"
                  value={riskInputs.baseScore}
                  readOnly
                  min="0"
                  max="10"
                  step="0.1"
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="exploitAvailable"
                    checked={riskInputs.exploitAvailable}
                    onCheckedChange={(checked) =>
                      updateRiskInput("exploitAvailable", checked as boolean)
                    }
                  />
                  <Label
                    htmlFor="exploitAvailable"
                    className="text-sm font-medium text-gray-700"
                  >
                    Exploit Available?
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="exploitedInWild"
                    checked={riskInputs.exploitedInWild}
                    onCheckedChange={(checked) =>
                      updateRiskInput("exploitedInWild", checked as boolean)
                    }
                  />
                  <Label
                    htmlFor="exploitedInWild"
                    className="text-sm font-medium text-gray-700"
                  >
                    Exploited in the Wild?
                  </Label>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="asset-criticality" className="text-blue-600">
                  Asset Criticality
                </Label>
                <Select
                  value={riskInputs.assetCriticality.toString()}
                  onValueChange={(value) =>
                    updateRiskInput("assetCriticality", parseInt(value))
                  }
                >
                  <SelectTrigger id="asset-criticality">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Low</SelectItem>
                    <SelectItem value="2">2 - Moderate</SelectItem>
                    <SelectItem value="3">3 - Important</SelectItem>
                    <SelectItem value="4">4 - High</SelectItem>
                    <SelectItem value="5">5 - Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="business-impact" className="text-blue-600">
                  Business Impact
                </Label>
                <Select
                  value={riskInputs.businessImpact.toString()}
                  onValueChange={(value) =>
                    updateRiskInput("businessImpact", parseInt(value))
                  }
                >
                  <SelectTrigger id="business-impact">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Low</SelectItem>
                    <SelectItem value="2">2 - Moderate</SelectItem>
                    <SelectItem value="3">3 - Important</SelectItem>
                    <SelectItem value="4">4 - High</SelectItem>
                    <SelectItem value="5">5 - Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="exposure-level" className="text-blue-600">
                  Exposure Level
                </Label>
                <Select
                  value={riskInputs.exposureLevel.toString()}
                  onValueChange={(value) =>
                    updateRiskInput("exposureLevel", parseInt(value))
                  }
                >
                  <SelectTrigger id="exposure-level">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Internal Only</SelectItem>
                    <SelectItem value="3">3 - Partner Access</SelectItem>
                    <SelectItem value="5">5 - Internet Facing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mitigation-slider" className="text-blue-600">
                🎚️ Mitigation Strength:{" "}
                {Math.round(riskInputs.mitigationStrength * 100)}%
              </Label>
              <Slider
                id="mitigation-slider"
                min={0}
                max={0.9}
                step={0.1}
                value={[riskInputs.mitigationStrength]}
                onValueChange={(value) =>
                  updateRiskInput("mitigationStrength", value[0])
                }
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-blue-600">💡 Mitigation Presets</Label>
              <div className="flex flex-wrap gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setMitigationPreset(0)}
                  className="bg-gray-500 hover:bg-gray-600 text-white"
                >
                  None
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setMitigationPreset(0.3)}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white"
                >
                  Basic
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setMitigationPreset(0.5)}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Moderate
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setMitigationPreset(0.7)}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  Strong
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setMitigationPreset(0.9)}
                  className="bg-purple-500 hover:bg-purple-600 text-white"
                >
                  Max
                </Button>
              </div>
            </div>

            <div className="text-center space-y-4">
              <Button
                type="button"
                onClick={calculateRisk}
                className="bg-green-600 hover:bg-green-700 text-lg font-semibold px-6 py-3"
                size="lg"
              >
                🧮 Calculate Risk
              </Button>

              {/* Save Calculation Section */}
              {riskResults && (
                <div className="border-t pt-4 space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="calculation-name" className="text-blue-600">
                      💾 Save Calculation (Optional Name)
                    </Label>
                    <Input
                      id="calculation-name"
                      type="text"
                      placeholder="Enter a name for this calculation..."
                      value={calculationName}
                      onChange={(e) => setCalculationName(e.target.value)}
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={saveCalculation}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700"
                    size="sm"
                  >
                    {isSaving ? "💾 Saving..." : "💾 Save to Database"}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Dynamic Mitigation Controls */}

        {/* Residual Vector Display */}
        {riskResults && (
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Label htmlFor="residual-vector" className="text-blue-600">
                  📑 <strong>Residual Vector (for Reporting)</strong>
                  <span className="font-normal text-gray-600 text-sm ml-2">
                    (copy for audit/report)
                  </span>
                </Label>
                <Input
                  id="residual-vector"
                  type="text"
                  className="font-mono bg-gray-50 text-sm"
                  value={riskResults.residualVector}
                  readOnly
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Risk Results */}
        {riskResults && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-blue-600">📊 Risk Results</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Highlight Row */}
                <div className="bg-yellow-100 border-2 border-yellow-400 rounded-lg p-4 mb-4 flex flex-wrap justify-center gap-4 items-center">
                  <div className="text-center">
                    <span className="text-sm font-medium text-yellow-800">
                      Base Score:
                    </span>
                    <div className="bg-yellow-50 border border-yellow-300 rounded px-3 py-1 mt-1">
                      <span className="font-bold text-yellow-900">
                        {riskResults.baseScore.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <span className="text-sm font-medium text-yellow-800">
                      Environmental Score:
                    </span>
                    <div className="bg-yellow-50 border border-yellow-300 rounded px-3 py-1 mt-1">
                      <span className="font-bold text-yellow-900">
                        {riskResults.envScoreAfter.toFixed(2)}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <span className="text-sm font-medium text-yellow-800">
                      Score Change (Δ):
                    </span>
                    <div className="bg-yellow-50 border border-yellow-300 rounded px-3 py-1 mt-1">
                      <span className="font-bold text-yellow-900">
                        {riskResults.deltaScore.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Before/After Table */}
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Metric</th>
                      <th className="text-center py-2">Before Mitigation</th>
                      <th className="text-center py-2">After Mitigation</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="py-2 font-medium">Risk Level</td>
                      <td className="text-center py-2">
                        {riskResults.riskLevelBefore}
                      </td>
                      <td className="text-center py-2">
                        {riskResults.riskLevelAfter}
                      </td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-2 font-medium">Likelihood</td>
                      <td className="text-center py-2">
                        {riskResults.likelihoodBefore.toFixed(2)}
                      </td>
                      <td className="text-center py-2">
                        {riskResults.likelihoodAfter.toFixed(2)}
                      </td>
                    </tr>
                    <tr>
                      <td className="py-2 font-medium">Impact</td>
                      <td className="text-center py-2">
                        {riskResults.impactBefore.toFixed(2)}
                      </td>
                      <td className="text-center py-2">
                        {riskResults.impactAfter.toFixed(2)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-blue-600">
                  🔍 Mitigation Effect
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p>
                    <strong>Score Reduction:</strong>{" "}
                    {riskResults.deltaScore.toFixed(2)}
                  </p>
                  <p>
                    <strong>Impact Reduction:</strong>{" "}
                    {(
                      riskResults.impactBefore - riskResults.impactAfter
                    ).toFixed(2)}
                  </p>
                  <p>
                    <strong>Risk Level Change:</strong>{" "}
                    {riskResults.riskLevelBefore === riskResults.riskLevelAfter
                      ? "No Change"
                      : `${riskResults.riskLevelBefore} → ${riskResults.riskLevelAfter}`}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* VSOC Analysis Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">
              📝 VSOC Threat Impact Analysis & Business Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="security-overview" className="text-blue-600">
                  <strong>Security Risk Posture Overview</strong>
                </Label>
                <Textarea
                  id="security-overview"
                  rows={5}
                  placeholder="Summarize key security risks, risk posture, and notable findings here..."
                  value={securityRiskOverview}
                  onChange={(e) => setSecurityRiskOverview(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="vendor-ai-score" className="text-blue-600">
                  <strong>VendorAI Score</strong>
                </Label>
                <Textarea
                  id="vendor-ai-score"
                  rows={5}
                  placeholder="Notes on VendorAI score, caveats, special context, or review..."
                  value={vendorAIScore}
                  onChange={(e) => setVendorAIScore(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
