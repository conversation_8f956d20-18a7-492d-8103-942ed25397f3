"use client";

import { usePara<PERSON>, useSearchParams } from "next/navigation";
import { useState, useEffect, useMemo } from "react";
import axios from "axios";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { isAfter, isBefore, parseISO } from "date-fns";
import { FormDatePicker } from "@/components/form/FormDatePicker";
import VendorCalculatedData from "@/elements/vendor-calculated/calculated-data";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { trpc } from "@/providers/Providers";
import { toastPromise } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";

interface CVEResult {
  Asset: string;
  "CVE ID": string;
  Published: string;
  Year: string;
  Month: string;
  "CVSS Score": string | number;
  Description: string;
  Source: string;
  AV: string;
  AC: string;
  PR: string;
  UI: string;
  C: string;
  I: string;
  A: string;
  metrics: {};
}

const dateFilterSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export default function Inventory() {
  const params = useParams();
  const para = useSearchParams();
  const company = para.get("company");
  const vendor = para.get("vendor");
  const asset = para.get("asset");
  const assessment = params.assessment as string;
  const [results, setResults] = useState<CVEResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState<any>(null);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [selectedCVE, setSelectedCVE] = useState<any>(null);
  const [selectedCalculation, setSelectedCalculation] = useState<any>(null);
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [comparisonComment, setComparisonComment] = useState<string>("");
  const [isVulnerable, setIsVulnerable] = useState<boolean>(false);
  const [isSavingComparison, setIsSavingComparison] = useState<boolean>(false);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof CVEResult | null;
    direction: "asc" | "desc";
  }>({ key: null, direction: "asc" });
  const { mutateAsync } = trpc.saveNVD.saveNVDData.useMutation();
  // Form for date filtering
  const form = useForm({
    resolver: zodResolver(dateFilterSchema),
    defaultValues: {
      startDate: undefined,
      endDate: undefined,
    },
  });

  const { watch } = form;
  const startDate = watch("startDate");
  const endDate = watch("endDate");

  const NVD_API_URL = "https://services.nvd.nist.gov/rest/json/cves/2.0";
  const HEADERS = { "User-Agent": "Mozilla/5.0" };

  function extractCvssScore(metrics: any): string | number {
    for (let key of ["cvssMetricV31", "cvssMetricV30", "cvssMetricV2"]) {
      const vector = metrics[key];
      if (vector && vector[0]?.cvssData) {
        return vector[0].cvssData.baseScore || "";
      }
    }
    return "";
  }

  function extractImpactMetrics(metrics: any) {
    for (let key of ["cvssMetricV31", "cvssMetricV30", "cvssMetricV2"]) {
      const vector = metrics[key];
      if (vector && vector[0]?.cvssData) {
        const data = vector[0].cvssData;
        return {
          AV: data.attackVector || "",
          AC: data.attackComplexity || "",
          PR: data.privilegesRequired || "",
          UI: data.userInteraction || "",
          C: data.confidentialityImpact || "",
          I: data.integrityImpact || "",
          A: data.availabilityImpact || "",
        };
      }
    }
    return { AV: "", AC: "", PR: "", UI: "", C: "", I: "", A: "" };
  }

  function analyzeRiskSummary(data: CVEResult[]) {
    const counter = { High: 0, Medium: 0, Low: 0, None: 0 };
    const vendors: Record<string, number> = {};
    const words: Record<string, number> = {};

    data.forEach((row) => {
      const score = parseFloat(String(row["CVSS Score"]) || "0");
      if (score >= 9) counter.High++;
      else if (score >= 7) counter.Medium++;
      else if (score >= 4) counter.Low++;
      else counter.None++;

      vendors[row.Source] = (vendors[row.Source] || 0) + 1;

      const descWords =
        (row.Description || "").toLowerCase().match(/\b[a-z]{4,}\b/g) || [];
      descWords.forEach((w) => {
        words[w] = (words[w] || 0) + 1;
      });
    });

    const topVendors = Object.entries(vendors)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map((v) => v[0]);

    const topWords = Object.entries(words)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map((w) => w[0]);

    return { cvss: counter, vendors: topVendors, keywords: topWords };
  }

  async function getCvesForAsset(assetName: string) {
    let results: CVEResult[] = [];
    let startIndex = 0;
    let totalResults = 1;

    setLoading(true);

    while (startIndex < totalResults) {
      try {
        const res = await axios.get(NVD_API_URL, {
          headers: HEADERS,
          params: {
            keywordSearch: assetName,
            startIndex,
            resultsPerPage: 200,
          },
          timeout: 30000, // 30 second timeout
        });

        const data = res.data;
        console.log(data);
        totalResults = data.totalResults || 0;

        if (!data.vulnerabilities || data.vulnerabilities.length === 0) {
          break;
        }

        (data.vulnerabilities || []).forEach((v: any) => {
          const cve = v.cve;
          const pubDate = cve.published || "";
          const year = pubDate.slice(0, 4);
          const month = pubDate.slice(5, 7);
          const metrics = cve.metrics;
          const impact = extractImpactMetrics(cve.metrics || {});
          results.push({
            Asset: assetName,
            "CVE ID": cve.id || "",
            Published: pubDate,
            Year: year,
            Month: month,
            "CVSS Score": extractCvssScore(cve.metrics || {}),
            Description:
              (cve.descriptions || []).find((d: any) => d.lang === "en")
                ?.value || "No description",
            Source: cve.sourceIdentifier || "",
            metrics: cve.metrics,
            ...impact,
          });
        });

        startIndex += 200;

        // Add a small delay to avoid rate limiting
        if (startIndex < totalResults) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      } catch (err: any) {
        console.error("API error:", err.message);
        break;
      }
    }

    setResults(results);
    setSummary(analyzeRiskSummary(results));
    setLoading(false);
    return results;
  }

  useEffect(() => {
    if (asset) {
      getCvesForAsset(asset);
    }
  }, [asset]);

  const handleSort = (key: keyof CVEResult) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const toggleRowExpansion = (index: number) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(index)) {
      newExpandedRows.delete(index);
    } else {
      newExpandedRows.add(index);
    }
    setExpandedRows(newExpandedRows);
  };

  const handleCVESelection = (cve: any) => {
    setSelectedCVE(cve);
    if (selectedCalculation) {
      setShowComparison(true);
    }
  };

  const handleCalculationSelection = (calculation: any) => {
    setSelectedCalculation(calculation);
    if (selectedCVE) {
      setShowComparison(true);
    }
  };

  const closeComparison = () => {
    setShowComparison(false);
    setSelectedCVE(null);
    setSelectedCalculation(null);
    setComparisonComment("");
  };

  // tRPC mutation for updating calculator data

  // Save comparison data with comments
  const saveComparisonWithComment = async () => {
    if (!selectedCVE || !selectedCalculation) {
      return;
    }

    setIsSavingComparison(true);

    // Create the data structure expected by saveNVD mutation
    const saveData = {
      assessment,
      vendor: vendor ?? "",
      company: company ?? "",
      cvssCalculator: selectedCalculation._id,
      nvdData: {
        cveId: selectedCVE["CVE ID"],
        cvssScore: selectedCVE["CVSS Score"],
        published: selectedCVE.Published,
        description: selectedCVE.Description,
        source: selectedCVE.Source,
        metrics: selectedCVE.metrics,
        calculatorData: {
          baseScore: selectedCalculation.riskResults.baseScore,
          envScoreBefore: selectedCalculation.riskResults.envScoreBefore,
          envScoreAfter: selectedCalculation.riskResults.envScoreAfter,
          deltaScore: selectedCalculation.riskResults.deltaScore,
          riskLevelBefore: selectedCalculation.riskResults.riskLevelBefore,
          riskLevelAfter: selectedCalculation.riskResults.riskLevelAfter,
        },
      },
      asset: asset ?? "",
      comments: comparisonComment,
      isVulnerability: isVulnerable,
    };

    toastPromise({
      asyncFunc: mutateAsync(saveData),
      success: "Comparison data saved successfully!",
      error: "Failed to save comparison data",
      onSuccess: () => {
        setComparisonComment("");
        setIsSavingComparison(false);
      },
      onError: (error) => {
        console.error("Save error:", error);
        setIsSavingComparison(false);
      },
    });
  };

  const sortedAndFilteredResults = useMemo(() => {
    let filteredData = [...results];

    // Apply date filtering
    if (startDate || endDate) {
      filteredData = filteredData.filter((cve) => {
        if (!cve.Published) return true; // Keep items without published date

        try {
          const publishedDate = parseISO(cve.Published);

          if (startDate && isBefore(publishedDate, startDate)) {
            return false;
          }

          if (endDate && isAfter(publishedDate, endDate)) {
            return false;
          }

          return true;
        } catch (error) {
          // If date parsing fails, keep the item
          return true;
        }
      });
    }

    // Apply sorting
    if (sortConfig.key) {
      filteredData = filteredData.sort((a, b) => {
        const aValue = a[sortConfig.key!];
        const bValue = b[sortConfig.key!];

        if (sortConfig.key === "Published") {
          const aDate = new Date(aValue as string);
          const bDate = new Date(bValue as string);
          return sortConfig.direction === "asc"
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime();
        }

        if (sortConfig.key === "CVSS Score") {
          const aScore = Number(aValue) || 0;
          const bScore = Number(bValue) || 0;
          return sortConfig.direction === "asc"
            ? aScore - bScore
            : bScore - aScore;
        }

        // String sorting for CVE ID and Description
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (aStr < bStr) return sortConfig.direction === "asc" ? -1 : 1;
        if (aStr > bStr) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    return filteredData;
  }, [results, sortConfig, startDate, endDate]);

  const getSortIcon = (columnKey: keyof CVEResult) => {
    if (sortConfig.key !== columnKey) {
      return "↕️";
    }
    return sortConfig.direction === "asc" ? "↑" : "↓";
  };

  console.log(selectedCalculation);

  return (
    <div className="p-6 w-full  space-y-6">
      {/* Header Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset</h1>
            {asset && (
              <p className="text-lg text-gray-600 mt-1">
                Asset:{" "}
                <span className="font-semibold text-blue-600">{asset}</span>
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>Assessment ID:</span>
            <code className="bg-gray-100 px-2 py-1 rounded text-xs">
              {assessment}
            </code>
          </div>
        </div>
      </div>

      {/* CVSS Calculator Data Section */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            🧮 CVSS Risk Calculations
            <span className="text-sm font-normal text-gray-500">
              - Saved risk assessments for this asset
            </span>
          </h2>
        </div>
        <div className="p-6">
          <VendorCalculatedData
            assessment={assessment}
            company={company || ""}
            vendor={vendor || ""}
            onSelectionChange={handleCalculationSelection}
            selectedCalculation={selectedCalculation}
          />
        </div>
      </div>

      {/* Vulnerability Search Section */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            🔍 Vulnerability Database Search
            <span className="text-sm font-normal text-gray-500">
              - CVE database search results
            </span>
          </h2>
        </div>
        <div className="p-6">
          {loading ? (
            <div className="text-center py-10">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-4">Searching for vulnerabilities...</p>
            </div>
          ) : (
            <>
              {/* Date Filter Section */}
              <FormProvider {...form}>
                <div className="bg-white p-4 rounded-lg shadow mb-6">
                  <h2 className="text-lg font-semibold mb-3">
                    Filter by Published Date
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Date
                      </label>
                      <FormDatePicker
                        name="startDate"
                        placeholder="Select start date"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Date
                      </label>
                      <FormDatePicker
                        name="endDate"
                        placeholder="Select end date"
                      />
                    </div>
                  </div>
                  {(startDate || endDate) && (
                    <div className="mt-3 text-sm text-gray-600">
                      Showing vulnerabilities published{" "}
                      {startDate &&
                        `from ${new Date(startDate).toLocaleDateString()}`}
                      {startDate && endDate && " "}
                      {endDate &&
                        `to ${new Date(endDate).toLocaleDateString()}`}
                    </div>
                  )}
                </div>
              </FormProvider>

              {summary && (
                <div className="bg-white p-4 rounded-lg shadow mb-6">
                  <h2 className="text-xl font-semibold mb-3">Risk Summary</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h3 className="font-medium">CVSS Severity</h3>
                      <ul className="mt-2">
                        <li className="text-red-600">
                          High: {summary.cvss.High}
                        </li>
                        <li className="text-orange-500">
                          Medium: {summary.cvss.Medium}
                        </li>
                        <li className="text-yellow-500">
                          Low: {summary.cvss.Low}
                        </li>
                        <li className="text-gray-500">
                          None: {summary.cvss.None}
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-medium">Top Sources</h3>
                      <ul className="mt-2">
                        {summary.vendors.map((v: string, i: number) => (
                          <li key={i}>{v}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-medium">Common Keywords</h3>
                      <ul className="mt-2">
                        {summary.keywords.map((w: string, i: number) => (
                          <li key={i}>{w}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              <div className="bg-white rounded-lg shadow overflow-hidden">
                <h2 className="text-xl font-semibold p-4 border-b">
                  Found {sortedAndFilteredResults.length} vulnerabilities
                </h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort("CVE ID")}
                        >
                          CVE ID {getSortIcon("CVE ID")}
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort("Published")}
                        >
                          Published {getSortIcon("Published")}
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort("CVSS Score")}
                        >
                          CVSS Score {getSortIcon("CVSS Score")}
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort("Description")}
                        >
                          Description {getSortIcon("Description")}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sortedAndFilteredResults.map((cve, index) => {
                        const metrics = cve.metrics;

                        const isExpanded = expandedRows.has(index);
                        return (
                          <>
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                                <a
                                  href={`https://nvd.nist.gov/vuln/detail/${cve["CVE ID"]}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  {cve["CVE ID"]}
                                </a>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(cve.Published).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${
                              Number(cve["CVSS Score"]) >= 9
                                ? "bg-red-100 text-red-800"
                                : Number(cve["CVSS Score"]) >= 7
                                ? "bg-orange-100 text-orange-800"
                                : Number(cve["CVSS Score"]) >= 4
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                            }`}
                                >
                                  {cve["CVSS Score"]}
                                </span>
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-500 max-w-md">
                                <div className={isExpanded ? "" : "truncate"}>
                                  {cve.Description}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex space-x-2">
                                  <button
                                    type="button"
                                    onClick={() => toggleRowExpansion(index)}
                                    className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  >
                                    {isExpanded ? (
                                      <>
                                        <svg
                                          className="w-4 h-4 mr-1"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M5 15l7-7 7 7"
                                          />
                                        </svg>
                                        Less Details
                                      </>
                                    ) : (
                                      <>
                                        <svg
                                          className="w-4 h-4 mr-1"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M19 9l-7 7-7-7"
                                          />
                                        </svg>
                                        More Details
                                      </>
                                    )}
                                  </button>

                                  <button
                                    type="button"
                                    onClick={() => handleCVESelection(cve)}
                                    className={`inline-flex items-center px-3 py-1 border shadow-sm text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                                      selectedCVE?.["CVE ID"] === cve["CVE ID"]
                                        ? "border-blue-500 bg-blue-100 text-blue-700"
                                        : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                                    }`}
                                  >
                                    <svg
                                      className="w-4 h-4 mr-1"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                      />
                                    </svg>
                                    {selectedCVE?.["CVE ID"] === cve["CVE ID"]
                                      ? "Selected"
                                      : "Compare"}
                                  </button>
                                </div>
                              </td>
                            </tr>

                            {/* Expanded Details Row */}
                            {isExpanded && (
                              <tr className="bg-gray-50">
                                <td colSpan={5} className="px-6 py-4">
                                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                                      Detailed Information for {cve["CVE ID"]}
                                    </h4>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                      {/* Basic Information */}
                                      <div>
                                        <h5 className="font-medium text-gray-900 mb-2">
                                          Basic Information
                                        </h5>
                                        <div className="space-y-2 text-sm">
                                          <div>
                                            <span className="font-medium text-gray-600">
                                              CVE ID:
                                            </span>
                                            <span className="ml-2">
                                              {cve["CVE ID"]}
                                            </span>
                                          </div>
                                          <div>
                                            <span className="font-medium text-gray-600">
                                              Published:
                                            </span>
                                            <span className="ml-2">
                                              {new Date(
                                                cve.Published
                                              ).toLocaleDateString()}
                                            </span>
                                          </div>
                                          <div>
                                            <span className="font-medium text-gray-600">
                                              CVSS Score:
                                            </span>
                                            <span className="ml-2">
                                              {cve["CVSS Score"]}
                                            </span>
                                          </div>
                                          <div>
                                            <span className="font-medium text-gray-600">
                                              Severity:
                                            </span>
                                          </div>
                                        </div>
                                      </div>

                                      {/* CVSS Metrics */}
                                    </div>

                                    {/* Full Description */}
                                    <div className="mt-6">
                                      <h5 className="font-medium text-gray-900 mb-2">
                                        Full Description
                                      </h5>
                                      <p className="text-sm text-gray-700 leading-relaxed">
                                        {cve.Description}
                                      </p>
                                    </div>
                                    {/* More Details */}
                                    <div className="space-y-6">
                                      {/* CVSS Metrics Overview */}
                                      <div>
                                        <h5 className="text-lg font-semibold text-gray-900 mb-4">
                                          CVSS v3.1 Metrics
                                        </h5>
                                        <Table>
                                          <TableHeader>
                                            <TableRow>
                                              <TableHead>Metric</TableHead>
                                              <TableHead>Value</TableHead>
                                              <TableHead>Score</TableHead>
                                              <TableHead>Details</TableHead>
                                            </TableRow>
                                          </TableHeader>
                                          <TableBody>
                                            {/* @ts-ignore */}
                                            {cve?.metrics?.cvssMetricV31?.map(
                                              (metric: any, idxx: number) => (
                                                <>
                                                  {/* Base Score Row */}
                                                  <TableRow
                                                    key={`${idxx}-base`}
                                                    className="bg-blue-50"
                                                  >
                                                    <TableCell className="font-semibold">
                                                      Base Score
                                                    </TableCell>
                                                    <TableCell>
                                                      <span
                                                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                          metric.cvssData
                                                            .baseScore >= 9
                                                            ? "bg-red-100 text-red-800"
                                                            : metric.cvssData
                                                                .baseScore >= 7
                                                            ? "bg-orange-100 text-orange-800"
                                                            : metric.cvssData
                                                                .baseScore >= 4
                                                            ? "bg-yellow-100 text-yellow-800"
                                                            : "bg-green-100 text-green-800"
                                                        }`}
                                                      >
                                                        {
                                                          metric.cvssData
                                                            .baseScore
                                                        }{" "}
                                                        (
                                                        {
                                                          metric.cvssData
                                                            .baseSeverity
                                                        }
                                                        )
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>
                                                      {
                                                        metric.cvssData
                                                          .baseScore
                                                      }
                                                      /10
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                      {
                                                        metric.cvssData
                                                          .vectorString
                                                      }
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Attack Vector */}
                                                  <TableRow key={`${idxx}-av`}>
                                                    <TableCell>
                                                      Attack Vector (AV)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                                                        {
                                                          metric.cvssData
                                                            .attackVector
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .attackVector ===
                                                      "NETWORK"
                                                        ? "Remotely exploitable"
                                                        : metric.cvssData
                                                            .attackVector ===
                                                          "ADJACENT_NETWORK"
                                                        ? "Adjacent network access"
                                                        : metric.cvssData
                                                            .attackVector ===
                                                          "LOCAL"
                                                        ? "Local access required"
                                                        : "Physical access required"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Attack Complexity */}
                                                  <TableRow key={`${idxx}-ac`}>
                                                    <TableCell>
                                                      Attack Complexity (AC)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                                                        {
                                                          metric.cvssData
                                                            .attackComplexity
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .attackComplexity ===
                                                      "LOW"
                                                        ? "Specialized conditions not required"
                                                        : "Specialized conditions required"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Privileges Required */}
                                                  <TableRow key={`${idxx}-pr`}>
                                                    <TableCell>
                                                      Privileges Required (PR)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                                                        {
                                                          metric.cvssData
                                                            .privilegesRequired
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .privilegesRequired ===
                                                      "NONE"
                                                        ? "No privileges required"
                                                        : metric.cvssData
                                                            .privilegesRequired ===
                                                          "LOW"
                                                        ? "Low privileges required"
                                                        : "High privileges required"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* User Interaction */}
                                                  <TableRow key={`${idxx}-ui`}>
                                                    <TableCell>
                                                      User Interaction (UI)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                                                        {
                                                          metric.cvssData
                                                            .userInteraction
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .userInteraction ===
                                                      "NONE"
                                                        ? "No user interaction required"
                                                        : "User interaction required"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Scope */}
                                                  <TableRow
                                                    key={`${idxx}-scope`}
                                                  >
                                                    <TableCell>
                                                      Scope (S)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                                                        {metric.cvssData.scope}
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData.scope ===
                                                      "UNCHANGED"
                                                        ? "Impact limited to vulnerable component"
                                                        : "Impact extends beyond vulnerable component"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Impact Metrics */}
                                                  <TableRow
                                                    key={`${idxx}-c`}
                                                    className="bg-red-50"
                                                  >
                                                    <TableCell>
                                                      Confidentiality Impact (C)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span
                                                        className={`px-2 py-1 rounded text-sm ${
                                                          metric.cvssData
                                                            .confidentialityImpact ===
                                                          "HIGH"
                                                            ? "bg-red-100 text-red-800"
                                                            : metric.cvssData
                                                                .confidentialityImpact ===
                                                              "LOW"
                                                            ? "bg-yellow-100 text-yellow-800"
                                                            : "bg-green-100 text-green-800"
                                                        }`}
                                                      >
                                                        {
                                                          metric.cvssData
                                                            .confidentialityImpact
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .confidentialityImpact ===
                                                      "HIGH"
                                                        ? "Total information disclosure"
                                                        : metric.cvssData
                                                            .confidentialityImpact ===
                                                          "LOW"
                                                        ? "Some information disclosed"
                                                        : "No confidentiality impact"}
                                                    </TableCell>
                                                  </TableRow>

                                                  <TableRow
                                                    key={`${idxx}-i`}
                                                    className="bg-orange-50"
                                                  >
                                                    <TableCell>
                                                      Integrity Impact (I)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span
                                                        className={`px-2 py-1 rounded text-sm ${
                                                          metric.cvssData
                                                            .integrityImpact ===
                                                          "HIGH"
                                                            ? "bg-red-100 text-red-800"
                                                            : metric.cvssData
                                                                .integrityImpact ===
                                                              "LOW"
                                                            ? "bg-yellow-100 text-yellow-800"
                                                            : "bg-green-100 text-green-800"
                                                        }`}
                                                      >
                                                        {
                                                          metric.cvssData
                                                            .integrityImpact
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .integrityImpact ===
                                                      "HIGH"
                                                        ? "Total compromise of system integrity"
                                                        : metric.cvssData
                                                            .integrityImpact ===
                                                          "LOW"
                                                        ? "Some files can be modified"
                                                        : "No integrity impact"}
                                                    </TableCell>
                                                  </TableRow>

                                                  <TableRow
                                                    key={`${idxx}-a`}
                                                    className="bg-purple-50"
                                                  >
                                                    <TableCell>
                                                      Availability Impact (A)
                                                    </TableCell>
                                                    <TableCell>
                                                      <span
                                                        className={`px-2 py-1 rounded text-sm ${
                                                          metric.cvssData
                                                            .availabilityImpact ===
                                                          "HIGH"
                                                            ? "bg-red-100 text-red-800"
                                                            : metric.cvssData
                                                                .availabilityImpact ===
                                                              "LOW"
                                                            ? "bg-yellow-100 text-yellow-800"
                                                            : "bg-green-100 text-green-800"
                                                        }`}
                                                      >
                                                        {
                                                          metric.cvssData
                                                            .availabilityImpact
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>-</TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      {metric.cvssData
                                                        .availabilityImpact ===
                                                      "HIGH"
                                                        ? "Total shutdown of affected resource"
                                                        : metric.cvssData
                                                            .availabilityImpact ===
                                                          "LOW"
                                                        ? "Reduced performance or interruptions"
                                                        : "No availability impact"}
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Exploitability and Impact Scores */}
                                                  <TableRow
                                                    key={`${idxx}-exploit`}
                                                    className="bg-gray-50"
                                                  >
                                                    <TableCell className="font-semibold">
                                                      Exploitability Score
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
                                                        {
                                                          metric.exploitabilityScore
                                                        }
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>
                                                      {
                                                        metric.exploitabilityScore
                                                      }
                                                      /10
                                                    </TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      Likelihood of successful
                                                      exploit
                                                    </TableCell>
                                                  </TableRow>

                                                  <TableRow
                                                    key={`${idxx}-impact`}
                                                    className="bg-gray-50"
                                                  >
                                                    <TableCell className="font-semibold">
                                                      Impact Score
                                                    </TableCell>
                                                    <TableCell>
                                                      <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                                                        {metric.impactScore}
                                                      </span>
                                                    </TableCell>
                                                    <TableCell>
                                                      {metric.impactScore}/10
                                                    </TableCell>
                                                    <TableCell className="text-sm text-gray-600">
                                                      Potential impact if
                                                      exploited
                                                    </TableCell>
                                                  </TableRow>

                                                  {/* Source */}
                                                  <TableRow
                                                    key={`${idxx}-source`}
                                                  >
                                                    <TableCell>
                                                      Source
                                                    </TableCell>
                                                    <TableCell colSpan={3}>
                                                      <span className="font-mono text-xs text-gray-500">
                                                        {metric.source}
                                                      </span>
                                                    </TableCell>
                                                  </TableRow>
                                                </>
                                              )
                                            )}
                                          </TableBody>
                                        </Table>
                                      </div>

                                      {/* CVSS Vector Breakdown */}
                                      <div>
                                        <h5 className="text-lg font-semibold text-gray-900 mb-3">
                                          CVSS Vector Breakdown
                                        </h5>
                                        {/* @ts-expect-error */}
                                        {cve?.metrics?.cvssMetricV31?.map(
                                          (metric: any, idxx: number) => (
                                            <div
                                              key={`vector-${idxx}`}
                                              className="bg-gray-50 p-4 rounded-lg"
                                            >
                                              <div className="font-mono text-sm mb-2 break-all">
                                                {metric.cvssData.vectorString}
                                              </div>
                                              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                                                <div>
                                                  <strong>AV:</strong>{" "}
                                                  {metric.cvssData.attackVector}
                                                </div>
                                                <div>
                                                  <strong>AC:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .attackComplexity
                                                  }
                                                </div>
                                                <div>
                                                  <strong>PR:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .privilegesRequired
                                                  }
                                                </div>
                                                <div>
                                                  <strong>UI:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .userInteraction
                                                  }
                                                </div>
                                                <div>
                                                  <strong>S:</strong>{" "}
                                                  {metric.cvssData.scope}
                                                </div>
                                                <div>
                                                  <strong>C:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .confidentialityImpact
                                                  }
                                                </div>
                                                <div>
                                                  <strong>I:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .integrityImpact
                                                  }
                                                </div>
                                                <div>
                                                  <strong>A:</strong>{" "}
                                                  {
                                                    metric.cvssData
                                                      .availabilityImpact
                                                  }
                                                </div>
                                              </div>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>

                                    {/* External Links */}
                                    <div className="mt-6 flex space-x-4">
                                      <a
                                        href={`https://nvd.nist.gov/vuln/detail/${cve["CVE ID"]}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                      >
                                        <svg
                                          className="w-4 h-4 mr-2"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                          />
                                        </svg>
                                        View on NVD
                                      </a>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            )}
                          </>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Side-by-Side Comparison Modal */}
      {showComparison && selectedCVE && selectedCalculation && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[95vh] overflow-hidden border border-gray-200">
            {/* Enhanced Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-5 border-b flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-white bg-opacity-20 rounded-lg p-2">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">
                    Security Analysis Comparison
                  </h2>
                  <p className="text-blue-100 text-sm">
                    CVE Database vs Risk Calculator Assessment
                  </p>
                </div>
              </div>
              <Button
                type="button"
                onClick={closeComparison}
                variant="ghost"
                size="sm"
                className="text-white hover:text-gray-200 hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded-lg transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </div>

            {/* Content */}
            <div className="p-8 overflow-y-auto max-h-[calc(95vh-140px)] bg-gray-50">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* CVE Details */}
                <div className="bg-gradient-to-br from-red-50 to-red-100 border-2 border-red-200 rounded-xl p-6 shadow-lg">
                  <div className="bg-red-600 text-white rounded-lg p-3 mb-6 -mx-2 -mt-2">
                    <h3 className="text-lg font-bold flex items-center">
                      <svg
                        className="w-6 h-6 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                      CVE Vulnerability Analysis
                    </h3>
                    <p className="text-red-100 text-sm mt-1 font-medium">
                      {selectedCVE["CVE ID"]} • Published{" "}
                      {new Date(selectedCVE.Published).toLocaleDateString()}
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Basic Information
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">CVSS Score:</span>
                          <span
                            className={`px-2 py-1 rounded text-xs font-medium ${
                              Number(selectedCVE["CVSS Score"]) >= 9
                                ? "bg-red-100 text-red-800"
                                : Number(selectedCVE["CVSS Score"]) >= 7
                                ? "bg-orange-100 text-orange-800"
                                : Number(selectedCVE["CVSS Score"]) >= 4
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {selectedCVE["CVSS Score"]}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Severity:</span>
                          <span>{selectedCVE.Severity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Published:</span>
                          <span>
                            {new Date(
                              selectedCVE.Published
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Description
                      </h4>
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {selectedCVE.Description}
                      </p>
                    </div>

                    {selectedCVE.metrics?.cvssMetricV31 &&
                      selectedCVE.metrics.cvssMetricV31.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3">
                            CVSS Metrics
                          </h4>
                          <div className="space-y-4">
                            {selectedCVE.metrics.cvssMetricV31.map(
                              (metric: any, index: number) => (
                                <div
                                  key={index}
                                  className="bg-white border border-gray-200 rounded-lg p-4"
                                >
                                  {selectedCVE.metrics.cvssMetricV31.length >
                                    1 && (
                                    <h5 className="font-medium text-gray-800 mb-3 text-sm">
                                      Metric Set {index + 1} -{" "}
                                      {metric.source || "Unknown Source"}
                                    </h5>
                                  )}

                                  <div className="grid grid-cols-2 gap-3 text-sm mb-3">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Attack Vector (AV):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.attackVector}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Attack Complexity (AC):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.attackComplexity}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>
                                          Privileges Required (PR):
                                        </strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.privilegesRequired}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>User Interaction (UI):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.userInteraction}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Scope (S):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.scope}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Confidentiality (C):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.confidentialityImpact}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Integrity (I):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.integrityImpact}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">
                                        <strong>Availability (A):</strong>
                                      </span>
                                      <span className="font-medium">
                                        {metric.cvssData.availabilityImpact}
                                      </span>
                                    </div>
                                  </div>

                                  {/* Base Score Display */}
                                  <div className="flex justify-between items-center mb-3 p-2 bg-gray-50 rounded">
                                    <span className="text-gray-600 font-medium">
                                      Base Score:
                                    </span>
                                    <span
                                      className={`px-2 py-1 rounded text-sm font-bold ${
                                        metric.cvssData.baseScore >= 9
                                          ? "bg-red-100 text-red-800"
                                          : metric.cvssData.baseScore >= 7
                                          ? "bg-orange-100 text-orange-800"
                                          : metric.cvssData.baseScore >= 4
                                          ? "bg-yellow-100 text-yellow-800"
                                          : "bg-green-100 text-green-800"
                                      }`}
                                    >
                                      {metric.cvssData.baseScore} (
                                      {metric.cvssData.baseSeverity})
                                    </span>
                                  </div>

                                  {/* Vector String */}
                                  <div>
                                    <span className="text-gray-600 font-medium text-sm">
                                      Vector String:
                                    </span>
                                    <div className="font-mono text-xs bg-gray-100 p-2 rounded mt-1 break-all">
                                      {metric.cvssData.vectorString}
                                    </div>
                                  </div>

                                  {/* Additional metrics if available */}
                                  {(metric.exploitabilityScore ||
                                    metric.impactScore) && (
                                    <div className="mt-3 pt-3 border-t border-gray-200">
                                      <div className="grid grid-cols-2 gap-3 text-sm">
                                        {metric.exploitabilityScore && (
                                          <div className="flex justify-between">
                                            <span className="text-gray-600">
                                              Exploitability Score:
                                            </span>
                                            <span className="font-medium">
                                              {metric.exploitabilityScore}
                                            </span>
                                          </div>
                                        )}
                                        {metric.impactScore && (
                                          <div className="flex justify-between">
                                            <span className="text-gray-600">
                                              Impact Score:
                                            </span>
                                            <span className="font-medium">
                                              {metric.impactScore}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                  </div>
                </div>

                {/* Risk Calculator Details */}
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 rounded-xl p-6 shadow-lg">
                  <div className="bg-blue-600 text-white rounded-lg p-3 mb-6 -mx-2 -mt-2">
                    <h3 className="text-lg font-bold flex items-center">
                      <svg
                        className="w-6 h-6 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                      Risk Assessment Calculator
                    </h3>
                    <p className="text-blue-100 text-sm mt-1 font-medium">
                      {selectedCalculation.name ||
                        `Calculation ${selectedCalculation._id?.slice(
                          -6
                        )}`}{" "}
                      •
                      {new Date(
                        selectedCalculation.calculationDate
                      ).toLocaleDateString()}
                    </p>
                  </div>

                  <div className="space-y-4">
                    {/* Enhanced Risk Scores Section */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                          />
                        </svg>
                        Complete Risk Analysis
                      </h4>

                      {/* Core Risk Metrics Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        {/* Base Score */}
                        <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-blue-700">
                                Base Score
                              </span>
                              <div className="text-2xl font-bold text-blue-900 mt-1">
                                {selectedCalculation.riskResults.baseScore.toFixed(
                                  2
                                )}
                              </div>
                            </div>
                            <div
                              className={`px-3 py-1 rounded-full text-xs font-bold ${
                                selectedCalculation.riskResults.baseScore >= 9
                                  ? "bg-red-500 text-white"
                                  : selectedCalculation.riskResults.baseScore >=
                                    7
                                  ? "bg-orange-500 text-white"
                                  : selectedCalculation.riskResults.baseScore >=
                                    4
                                  ? "bg-yellow-500 text-white"
                                  : "bg-green-500 text-white"
                              }`}
                            >
                              {selectedCalculation.riskResults.baseScore >= 9
                                ? "CRITICAL"
                                : selectedCalculation.riskResults.baseScore >= 7
                                ? "HIGH"
                                : selectedCalculation.riskResults.baseScore >= 4
                                ? "MEDIUM"
                                : "LOW"}
                            </div>
                          </div>
                        </div>

                        {/* Risk Reduction */}
                        <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-green-700">
                                Risk Reduction
                              </span>
                              <div className="text-2xl font-bold text-green-900 mt-1">
                                -
                                {selectedCalculation.riskResults.deltaScore.toFixed(
                                  3
                                )}
                              </div>
                            </div>
                            <div className="text-green-600">
                              <svg
                                className="w-8 h-8"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Environmental Scores */}
                      <div className="mb-6">
                        <h5 className="text-sm font-semibold text-gray-800 mb-3">
                          Environmental Scores
                        </h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-red-700">
                                Before Mitigation
                              </span>
                              <span className="text-lg font-bold text-red-600">
                                {selectedCalculation.riskResults.envScoreBefore.toFixed(
                                  3
                                )}
                              </span>
                            </div>
                          </div>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-green-700">
                                After Mitigation
                              </span>
                              <span className="text-lg font-bold text-green-600">
                                {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                  3
                                )}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Likelihood Analysis */}
                      <div className="mb-6">
                        <h5 className="text-sm font-semibold text-gray-800 mb-3">
                          Likelihood Analysis
                        </h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-orange-700">
                                Likelihood Before
                              </span>
                              <span className="text-lg font-bold text-orange-600">
                                {selectedCalculation.riskResults.likelihoodBefore.toFixed(
                                  3
                                )}
                              </span>
                            </div>
                            <div className="mt-2">
                              <div className="w-full bg-orange-200 rounded-full h-2">
                                <div
                                  className="bg-orange-500 h-2 rounded-full"
                                  style={{
                                    width: `${
                                      selectedCalculation.riskResults
                                        .likelihoodBefore * 100
                                    }%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-blue-700">
                                Likelihood After
                              </span>
                              <span className="text-lg font-bold text-blue-600">
                                {selectedCalculation.riskResults.likelihoodAfter.toFixed(
                                  3
                                )}
                              </span>
                            </div>
                            <div className="mt-2">
                              <div className="w-full bg-blue-200 rounded-full h-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{
                                    width: `${
                                      selectedCalculation.riskResults
                                        .likelihoodAfter * 100
                                    }%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Impact Analysis */}
                      <div className="mb-6">
                        <h5 className="text-sm font-semibold text-gray-800 mb-3">
                          Impact Analysis
                        </h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-purple-700">
                                Impact Before
                              </span>
                              <span className="text-lg font-bold text-purple-600">
                                {selectedCalculation.riskResults.impactBefore.toFixed(
                                  2
                                )}
                              </span>
                            </div>
                            <div className="mt-2">
                              <div className="w-full bg-purple-200 rounded-full h-2">
                                <div
                                  className="bg-purple-500 h-2 rounded-full"
                                  style={{
                                    width: `${
                                      selectedCalculation.riskResults
                                        .impactBefore * 100
                                    }%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                          <div className="bg-teal-50 border border-teal-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-teal-700">
                                Impact After
                              </span>
                              <span className="text-lg font-bold text-teal-600">
                                {selectedCalculation.riskResults.impactAfter.toFixed(
                                  2
                                )}
                              </span>
                            </div>
                            <div className="mt-2">
                              <div className="w-full bg-teal-200 rounded-full h-2">
                                <div
                                  className="bg-teal-500 h-2 rounded-full"
                                  style={{
                                    width: `${
                                      selectedCalculation.riskResults
                                        .impactAfter * 100
                                    }%`,
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Risk Level Comparison */}
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-800 mb-3">
                          Risk Level Transition
                        </h5>
                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-center">
                              <span className="text-xs text-gray-600 block mb-1">
                                Before
                              </span>
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-bold ${
                                  selectedCalculation.riskResults
                                    .riskLevelBefore === "Very High"
                                    ? "bg-red-500 text-white"
                                    : selectedCalculation.riskResults
                                        .riskLevelBefore === "High"
                                    ? "bg-orange-500 text-white"
                                    : selectedCalculation.riskResults
                                        .riskLevelBefore === "Moderate"
                                    ? "bg-yellow-500 text-white"
                                    : "bg-green-500 text-white"
                                }`}
                              >
                                {
                                  selectedCalculation.riskResults
                                    .riskLevelBefore
                                }
                              </span>
                            </div>
                            <div className="flex-1 mx-4">
                              <div className="flex items-center">
                                <div className="flex-1 h-0.5 bg-gray-300"></div>
                                <svg
                                  className="w-6 h-6 mx-2 text-gray-400"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                                  />
                                </svg>
                                <div className="flex-1 h-0.5 bg-gray-300"></div>
                              </div>
                            </div>
                            <div className="text-center">
                              <span className="text-xs text-gray-600 block mb-1">
                                After
                              </span>
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-bold ${
                                  selectedCalculation.riskResults
                                    .riskLevelAfter === "Very High"
                                    ? "bg-red-500 text-white"
                                    : selectedCalculation.riskResults
                                        .riskLevelAfter === "High"
                                    ? "bg-orange-500 text-white"
                                    : selectedCalculation.riskResults
                                        .riskLevelAfter === "Moderate"
                                    ? "bg-yellow-500 text-white"
                                    : "bg-green-500 text-white"
                                }`}
                              >
                                {selectedCalculation.riskResults.riskLevelAfter}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        CVSS Vector
                      </h4>
                      <div className="font-mono text-xs bg-gray-100 p-2 rounded break-all">
                        {selectedCalculation.cvssVector}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Residual Vector
                      </h4>
                      <div className="font-mono text-xs bg-gray-100 p-2 rounded break-all">
                        {selectedCalculation.riskResults.residualVector}
                      </div>
                    </div>

                    {selectedCalculation.securityRiskOverview && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          Security Risk Overview
                        </h4>
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {selectedCalculation.securityRiskOverview}
                        </p>
                      </div>
                    )}

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Calculation Date
                      </h4>
                      <p className="text-sm text-gray-600">
                        {new Date(
                          selectedCalculation.calculationDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Mitigation Controlls
                      </h4>
                      <div className="space-y-6">
                        {/* Network Controls */}
                        {selectedCalculation?.mitigationControls?.Network &&
                          selectedCalculation.mitigationControls.Network
                            .length > 0 && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                              <h5 className="font-semibold text-blue-900 mb-3 flex items-center">
                                <svg
                                  className="w-4 h-4 mr-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"
                                  />
                                </svg>
                                Network Controls
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {selectedCalculation.mitigationControls.Network.map(
                                  (item: any, iddf: any) => (
                                    <div
                                      key={iddf}
                                      className={`flex items-center justify-between p-2 rounded-md ${
                                        item.checked
                                          ? "bg-green-100 border border-green-300"
                                          : "bg-gray-100 border border-gray-300"
                                      }`}
                                    >
                                      <span className="text-sm font-medium text-gray-800">
                                        {item.name}
                                      </span>
                                      <span className="text-lg">
                                        {item.checked ? (
                                          <span className="text-green-600">
                                            ✅
                                          </span>
                                        ) : (
                                          <span className="text-gray-400">
                                            ⭕
                                          </span>
                                        )}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Application Controls */}
                        {selectedCalculation?.mitigationControls?.Application &&
                          selectedCalculation.mitigationControls.Application
                            .length > 0 && (
                            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                              <h5 className="font-semibold text-purple-900 mb-3 flex items-center">
                                <svg
                                  className="w-4 h-4 mr-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                                Application Controls
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {selectedCalculation.mitigationControls.Application.map(
                                  (item: any, iddf: any) => (
                                    <div
                                      key={iddf}
                                      className={`flex items-center justify-between p-2 rounded-md ${
                                        item.checked
                                          ? "bg-green-100 border border-green-300"
                                          : "bg-gray-100 border border-gray-300"
                                      }`}
                                    >
                                      <span className="text-sm font-medium text-gray-800">
                                        {item.name}
                                      </span>
                                      <span className="text-lg">
                                        {item.checked ? (
                                          <span className="text-green-600">
                                            ✅
                                          </span>
                                        ) : (
                                          <span className="text-gray-400">
                                            ⭕
                                          </span>
                                        )}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Database Controls */}
                        {selectedCalculation?.mitigationControls?.Database &&
                          selectedCalculation.mitigationControls.Database
                            .length > 0 && (
                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                              <h5 className="font-semibold text-orange-900 mb-3 flex items-center">
                                <svg
                                  className="w-4 h-4 mr-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
                                  />
                                </svg>
                                Database Controls
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {selectedCalculation.mitigationControls.Database.map(
                                  (item: any, iddf: any) => (
                                    <div
                                      key={iddf}
                                      className={`flex items-center justify-between p-2 rounded-md ${
                                        item.checked
                                          ? "bg-green-100 border border-green-300"
                                          : "bg-gray-100 border border-gray-300"
                                      }`}
                                    >
                                      <span className="text-sm font-medium text-gray-800">
                                        {item.name}
                                      </span>
                                      <span className="text-lg">
                                        {item.checked ? (
                                          <span className="text-green-600">
                                            ✅
                                          </span>
                                        ) : (
                                          <span className="text-gray-400">
                                            ⭕
                                          </span>
                                        )}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Encryption Controls */}
                        {selectedCalculation?.mitigationControls?.Encryption &&
                          selectedCalculation.mitigationControls.Encryption
                            .length > 0 && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                              <h5 className="font-semibold text-green-900 mb-3 flex items-center">
                                <svg
                                  className="w-4 h-4 mr-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                  />
                                </svg>
                                Encryption Controls
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {selectedCalculation.mitigationControls.Encryption.map(
                                  (item: any, iddf: any) => (
                                    <div
                                      key={iddf}
                                      className={`flex items-center justify-between p-2 rounded-md ${
                                        item.checked
                                          ? "bg-green-100 border border-green-300"
                                          : "bg-gray-100 border border-gray-300"
                                      }`}
                                    >
                                      <span className="text-sm font-medium text-gray-800">
                                        {item.name}
                                      </span>
                                      <span className="text-lg">
                                        {item.checked ? (
                                          <span className="text-green-600">
                                            ✅
                                          </span>
                                        ) : (
                                          <span className="text-gray-400">
                                            ⭕
                                          </span>
                                        )}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Summary Stats */}
                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <h5 className="font-semibold text-gray-900 mb-3 flex items-center">
                            <svg
                              className="w-4 h-4 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                              />
                            </svg>
                            Mitigation Summary
                          </h5>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {[
                              "Network",
                              "Application",
                              "Database",
                              "Encryption",
                            ].map((category) => {
                              const controls =
                                selectedCalculation?.mitigationControls?.[
                                  category
                                ] || [];
                              const activeControls = controls.filter(
                                // @ts-expect-error
                                (item) => item.checked
                              ).length;
                              const totalControls = controls.length;
                              const percentage =
                                totalControls > 0
                                  ? Math.round(
                                      (activeControls / totalControls) * 100
                                    )
                                  : 0;

                              return (
                                <div key={category} className="text-center">
                                  <div className="text-2xl font-bold text-gray-900">
                                    {activeControls}/{totalControls}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {category}
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div
                                      className={`h-2 rounded-full ${
                                        percentage >= 80
                                          ? "bg-green-500"
                                          : percentage >= 50
                                          ? "bg-yellow-500"
                                          : percentage > 0
                                          ? "bg-orange-500"
                                          : "bg-gray-300"
                                      }`}
                                      style={{ width: `${percentage}%` }}
                                    ></div>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {percentage}%
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Vendor AI Score
                      </h4>
                      <p className="text-sm text-gray-600">
                        {selectedCalculation.vendorAIScore}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Calculation Date
                      </h4>
                      <p className="text-sm text-gray-600">
                        {new Date(
                          selectedCalculation.calculationDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Comparison Summary */}
              <div className="mt-8 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center">
                    <svg
                      className="w-8 h-8 mr-3 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    Comparative Analysis Summary
                  </h3>
                  <p className="text-gray-600">
                    Key metrics comparison between CVE data and risk assessment
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white rounded-xl p-6 text-center shadow-md border border-blue-200">
                    <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 21l3-3 9-9a1.414 1.414 0 000-2l-2-2a1.414 1.414 0 00-2 0l-9 9-3 3v4h4z"
                        />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {Math.abs(
                        Number(selectedCVE["CVSS Score"]) -
                          selectedCalculation.riskResults.baseScore
                      ).toFixed(1)}
                    </div>
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      Score Difference
                    </div>
                    <div className="text-xs text-gray-500">
                      CVE vs Calculator Base Score
                    </div>
                  </div>

                  <div className="bg-white rounded-xl p-6 text-center shadow-md border border-green-200">
                    <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                        />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      -{selectedCalculation.riskResults.deltaScore.toFixed(1)}
                    </div>
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      Risk Reduction
                    </div>
                    <div className="text-xs text-gray-500">
                      Mitigation Impact
                    </div>
                  </div>

                  <div className="bg-white rounded-xl p-6 text-center shadow-md border border-purple-200">
                    <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-purple-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-purple-600 mb-2">
                      {selectedCalculation.riskResults.envScoreAfter.toFixed(1)}
                    </div>
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      Final Score
                    </div>
                    <div className="text-xs text-gray-500">
                      Post-Mitigation Risk
                    </div>
                  </div>
                </div>

                {/* Risk Level Comparison */}
                <div className="mt-6 bg-white rounded-xl p-4 border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3 text-center">
                    Risk Level Progression
                  </h4>
                  <div className="flex items-center justify-center space-x-4">
                    <div className="text-center">
                      <div
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          Number(selectedCVE["CVSS Score"]) >= 9
                            ? "bg-red-100 text-red-800"
                            : Number(selectedCVE["CVSS Score"]) >= 7
                            ? "bg-orange-100 text-orange-800"
                            : Number(selectedCVE["CVSS Score"]) >= 4
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        CVE: {selectedCVE.Severity}
                      </div>
                    </div>
                    <svg
                      className="w-6 h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                    <div className="text-center">
                      <div
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          selectedCalculation.riskResults.riskLevelBefore ===
                          "Very High"
                            ? "bg-red-100 text-red-800"
                            : selectedCalculation.riskResults
                                .riskLevelBefore === "High"
                            ? "bg-orange-100 text-orange-800"
                            : selectedCalculation.riskResults
                                .riskLevelBefore === "Moderate"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        Before:{" "}
                        {selectedCalculation.riskResults.riskLevelBefore}
                      </div>
                    </div>
                    <svg
                      className="w-6 h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                    <div className="text-center">
                      <div
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          selectedCalculation.riskResults.riskLevelAfter ===
                          "Very High"
                            ? "bg-red-100 text-red-800"
                            : selectedCalculation.riskResults.riskLevelAfter ===
                              "High"
                            ? "bg-orange-100 text-orange-800"
                            : selectedCalculation.riskResults.riskLevelAfter ===
                              "Moderate"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        After: {selectedCalculation.riskResults.riskLevelAfter}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Comment Section */}
                <div className="mt-8 bg-white border-2 border-gray-200 rounded-xl p-6 shadow-lg">
                  <div className="mb-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2 flex items-center">
                      <svg
                        className="w-5 h-5 mr-2 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"
                        />
                      </svg>
                      Analysis Comments
                    </h3>
                    <p className="text-sm text-gray-600">
                      Add your analysis notes and observations about this
                      comparison
                    </p>
                  </div>

                  <div className="space-y-4">
                    <Textarea
                      value={comparisonComment}
                      onChange={(e) => setComparisonComment(e.target.value)}
                      placeholder="Enter your analysis comments, observations, or recommendations based on the comparison between CVE data and risk calculator results..."
                      className="min-h-[120px] resize-none"
                      maxLength={2000}
                    />

                    <div className="flex items-center space-x-2 mt-4">
                      <Checkbox
                        id="isVulnerable"
                        checked={isVulnerable}
                        onCheckedChange={(checked) =>
                          setIsVulnerable(checked as boolean)
                        }
                      />
                      <label
                        htmlFor="isVulnerable"
                        className="text-sm font-medium text-gray-700 cursor-pointer"
                      >
                        Mark as vulnerability requiring attention
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        {comparisonComment.length}/2000 characters
                      </div>

                      <div className="flex space-x-3">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setComparisonComment("")}
                          disabled={!comparisonComment || isSavingComparison}
                        >
                          Clear
                        </Button>

                        <Button
                          type="button"
                          onClick={saveComparisonWithComment}
                          disabled={
                            !comparisonComment.trim() || isSavingComparison
                          }
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          {isSavingComparison ? (
                            <>
                              <svg
                                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              Saving...
                            </>
                          ) : (
                            <>
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                                />
                              </svg>
                              Save Analysis
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
