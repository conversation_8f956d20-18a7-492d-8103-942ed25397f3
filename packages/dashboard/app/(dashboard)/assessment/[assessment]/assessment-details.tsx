"use client";
import {
  <PERSON>,
  TableBody,
  Table<PERSON>ell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { trpc } from "@/providers/Providers";
import { useParams, useRouter, useSearchParams } from "next/navigation";

import { sendSubmissionScoreCardValidator } from "../../../../../shared/validators/assessment-submission.validator";
import { z } from "zod";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { cn, toastPromise } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Building2,
  Calculator,
  CheckCheck,
  ChevronDown,
  ChevronUp,
  Download,
  Edit,
  Flag,
  History,
  Mail,
  MessageSquare,
  Search,
  Shield,
  ShieldCheck,
  BarChart3,
  Zap,
  FileText,
  AlertTriangle,
  Users,
} from "lucide-react";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { FormTextField } from "@/components/form/FormTextField";
import { useMemo, useState } from "react";
import { SECTIONTYPE } from "../../../../../shared/types/Standard";

import { RouterOutput } from "../../../../../shared";
import VulnerabilityScoreForm from "@/elements/assessments/vulnerability-score.form";
import {
  VulnerabilityForm,
  useVulnerabilityScoreForm,
} from "@/hooks/forms/vulnerability";
import { USER_ROLE } from "../../../../../shared/types/User";
import { AuditorForm, useAuditorScoreForm } from "@/hooks/forms/auditor";
import { useRole } from "@/hooks/useRole";
import { ScrollArea } from "@/components/ui/scroll-area";
import { VulnerabilityDetailsSheet } from "@/sheets/assessment.sheet";
import { VulnerabilityResponseSheet } from "@/sheets/assessment.sheet/VulnerabilityResponseSheet";
import { VulnerabilityResolveAndExceptionSheet } from "@/sheets/assessment.sheet/VulnerabilityResolveAndExceptionSheet";
import {
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from "../../../../../shared/types/AssessmentSubmission";
import CompanyClosureApprovalForm from "@/elements/assessments/company-closure-approval.form";
import { useDownload } from "@/hooks/useDownload";
import VulnerabilityCloseSheet from "@/sheets/assessment.sheet/VulnerabilityCloseSheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { format, sub } from "date-fns";
import { CRITICALITY_LEVELS } from "packages/shared/types/Company";
import VendorCalculatedData from "@/elements/vendor-calculated/calculated-data";
import AssetUpdateForm, {
  useUpdateAssetFormMethods,
} from "@/elements/assessments/asset-update.form";
type VulnerabilityScoreForm = z.infer<typeof sendSubmissionScoreCardValidator>;

type Props = {
  userRole:
    | USER_ROLE.LEAD_SOC_ANALYST
    | USER_ROLE.SOC_ANALYST
    | USER_ROLE.AUDITOR
    | USER_ROLE.COMPANY;
};

const sectionTypeByUserRole = {
  [USER_ROLE.SOC_ANALYST]: [SECTIONTYPE.INVENTORY],
  [USER_ROLE.LEAD_SOC_ANALYST]: [SECTIONTYPE.INVENTORY],
  [USER_ROLE.AUDITOR]: [SECTIONTYPE.GENERAL],
  [USER_ROLE.COMPANY]: [SECTIONTYPE.GENERAL, SECTIONTYPE.INVENTORY],
} as const;

export default function AssessmentDetails({ userRole }: Props) {
  const roles = useRole();
  const { isAuditor, isLeadSoc, isSoc } = roles;
  const [socDialog, setSocDialog] = useState(false);
  const router = useRouter();

  const [auditorDialog, setAuditorDialog] = useState(false);

  const [selectedQuestion, setSelectedQuestion] = useState<string>();
  const [showHistorySheet, setShowHistorySheet] = useState(false);
  const [showCveSheet, setShowCveSheet] = useState(false);
  const [confirm, setConfirm] = useState(false);
  const [open, setOpen] = useState(false);
  const [answer, setAnswer] = useState<string>();

  const [exceptionDialog, setExceptionDialog] = useState(false);

  const [asset, setAsset] = useState<string>();

  const vulnerabilityMethods = useVulnerabilityScoreForm();
  const auditorMethods = useAuditorScoreForm();

  const { assessment } = useParams<{ assessment: string }>();

  const params = useSearchParams();

  const questions = params.getAll("question");

  const vScoreFormMethods = useForm<VulnerabilityScoreForm>({
    defaultValues: { assessment, message: "", vendor: "" },
    resolver: zodResolver(sendSubmissionScoreCardValidator),
  });

  const download = useDownload();

  const _assessment = trpc.assessment.getAssessment.useQuery({ assessment });

  const questionsFilter = useMemo(() => {
    return (_assessment.data?.assignments || [])
      .map((_) =>
        (_.questions || [])
          .filter((question) =>
            sectionTypeByUserRole[userRole].includes(
              // @ts-ignore
              question?.section?.sectionType || ""
            )
          )
          .map((_) => String(_._id))
      )
      .flat();
  }, [_assessment.data?.assignments, userRole]);

  const assessmentSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment, questions: questionsFilter },
      {
        enabled: !!questionsFilter.length,
        select(data) {
          // only show non bool questions to auditor
          if (userRole === USER_ROLE.AUDITOR) {
            return data.filter((items) => {
              return items.submission.question?.input.inputType !== "switch";
            });
          }
          return data;
        },
        onSuccess(data) {
          if (data.at(0)?.vendor)
            vScoreFormMethods.setValue(
              "vendor",
              // @ts-ignore
              String(data.at(0)?.vendor._id)
            );
        },
      }
    );

  const updateSocScore = trpc.assessment.manageVulnerabilityScore.useMutation();
  const updateAuditorScore =
    trpc.assessment.manageAuditScoreByAuditor.useMutation();

  const onSocModalOpen = (
    submission: RouterOutput["assessment"]["getAssessmentSubmissions"][number]
  ) => {
    const { _id } = submission;
    vulnerabilityMethods.setValue("submissionId", String(_id));
    setSocDialog(true);
  };

  const onAuditorModalOpen = (
    submission: RouterOutput["assessment"]["getAssessmentSubmissions"][number]
  ) => {
    const { _id } = submission;
    auditorMethods.setValue("submissionId", String(_id));
    setAuditorDialog(true);
  };

  const handleSocVulnerabilityScoreSubmit: SubmitHandler<VulnerabilityForm> = (
    data
  ) => {
    toastPromise({
      asyncFunc: updateSocScore.mutateAsync(data),
      success: "score update successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        vulnerabilityMethods.reset();
        setSocDialog(false);
      },
    });
  };

  const handleVulnerabilityUpdateRating = ({
    submission,
    assessment,
    vulnerability,
    _id,
  }: RouterOutput["assessment"]["getAssessmentSubmissions"][number]) => {
    vulnerabilityMethods.setValue(
      "criticalityLevel",
      vulnerability.criticalityLevel
    );
    vulnerabilityMethods.setValue("cvssScore", vulnerability.cvssScore);
    vulnerabilityMethods.setValue("remarks", vulnerability.remarks);
    vulnerabilityMethods.setValue("score", vulnerability.score);
    vulnerabilityMethods.setValue("submissionId", String(_id));
    setSocDialog(true);
  };

  // TODO: Refactor the code duplication
  const handleSocSubmitAndEmail: SubmitHandler<VulnerabilityForm> = (data) => {
    toastPromise({
      asyncFunc: updateSocScore.mutateAsync({ ...data, sendEmail: true }),
      success: "score update & mail sent successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        vulnerabilityMethods.reset();
        setSocDialog(false);
      },
    });
  };

  const handleAuditorSubmit: SubmitHandler<AuditorForm> = (data) => {
    toastPromise({
      asyncFunc: updateAuditorScore.mutateAsync(data),
      success: "score update successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        auditorMethods.reset();
      },
    });
  };

  const sendVcard = trpc.assessment.sendVulnerabilityScoreCard.useMutation();
  const handleSendVCardMail = vScoreFormMethods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: sendVcard.mutateAsync(data),
      success: "Mail sent successfully",
    });
  });

  const submissionHistory =
    trpc.assessment.getAssessmentSubmissionHistory.useQuery({
      question: selectedQuestion,
    });

  const showHistory = (questionId: string, answer: string) => {
    setSelectedQuestion(questionId);
    setShowHistorySheet(true);
    setAnswer(answer);
  };

  const filterHistory = submissionHistory.data?.filter(
    (h) => h.submissionSnapshot.submission.answer === answer
  );

  const handleRoute = (data: string) => {
    router.push(
      `/assessment/${assessment}/inventory?asset=${data}&company=${company}&vendor=${vendor}`
    );
  };

  const company = _assessment?.data?.company;
  const vendor = _assessment?.data?.vendor;

  console.log(company, vendor);
  const { data: allAssetCves } = trpc.saveNVD.getNVDAllData.useQuery({
    assessment,
    // @ts-expect-error
    company: company,
    // @ts-expect-error
    vendor: vendor,
    asset,
  });

  const { methods, onSubmit } = useUpdateAssetFormMethods();

  return (
    <div className="space-y-2 p-2 ">
      {/* Header Section with CVE Analysis Button */}
      <div className="flex items-center justify-between">
        {/* Left spacer for balance */}
        <div className="w-1/4"></div>

        {/* Centered text content */}
        <div className="flex-1 text-center">
          <h2 className="text-2xl font-bold text-white">Assessment Details</h2>
          <p className="text-gray-200 mt-1">
            Manage vulnerability assessments and risk analysis
          </p>
        </div>

        {/* Right-aligned buttons */}
        <div className="w-1/4 flex justify-end items-center space-x-3">
          <Button
            onClick={() => setOpen(!open)}
            variant={open ? "default" : "outline"}
            size="lg"
            className={`transition-all duration-200 px-6 py-2.5 rounded-xl font-medium shadow-sm hover:shadow-md ${
              open
                ? "bg-blue-600 hover:bg-blue-700 text-white border-0"
                : "border-2 border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 bg-white"
            }`}
          >
            <div className="flex items-center space-x-2">
              {open ? (
                <>
                  <ChevronUp className="w-4 h-4" />
                  <span className="hidden lg:inline">Hide Details</span>
                  <span className="lg:hidden">Hide</span>
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4" />
                  <span className="hidden lg:inline">Show Details</span>
                  <span className="lg:hidden">Show</span>
                </>
              )}
            </div>
          </Button>

          <Button
            onClick={() =>
              router.push(
                `/assessment/${assessment}/calculator?company=${company}&vendor=${vendor}`
              )
            }
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2.5 rounded-xl font-medium shadow-sm hover:shadow-md transition-all duration-200"
          >
            <div className="flex items-center space-x-2">
              <Calculator className="w-4 h-4" />
              <span className="hidden lg:inline">Calculate V.AI Score</span>
              <span className="lg:hidden">Calculate</span>
            </div>
          </Button>
        </div>
      </div>

      {/* Toggle Details Button */}

      {/* Enhanced Details Section */}
      <div
        className={`transition-all duration-300 ease-in-out overflow-hidden ${
          open ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div
          className={`transform transition-transform duration-300 ${
            open ? "translate-y-0" : "-translate-y-4"
          }`}
        >
          <div className="mt-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm">
            <div className="flex items-center space-x-3 mb-4">
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900">
                  Risk Assessment Details
                </h3>
                <p className="text-sm text-blue-700">
                  Comprehensive vulnerability analysis and calculations
                </p>
              </div>
            </div>

            <VendorCalculatedData
              assessment={assessment}
              // @ts-expect-error
              company={company}
              // @ts-expect-error
              vendor={vendor}
            />
          </div>
        </div>
      </div>
      <Dialog open={exceptionDialog} onOpenChange={setExceptionDialog}>
        <DialogContent className="min-w-[1000px]">
          <AssetUpdateForm methods={methods} onSubmit={onSubmit} />
        </DialogContent>
      </Dialog>
      <Dialog open={socDialog} onOpenChange={setSocDialog}>
        <DialogContent className="min-w-[1000px]">
          <VulnerabilityScoreForm
            methods={vulnerabilityMethods}
            handleSubmit={handleSocVulnerabilityScoreSubmit}
            handleSocSubmitAndEmail={handleSocSubmitAndEmail}
            assessment={assessment}
            // @ts-expect-error
            vendor={vendor}
            // @ts-expect-error
            company={company}
            asset={asset}
          />
        </DialogContent>
      </Dialog>
      <Sheet open={showCveSheet} onOpenChange={setShowCveSheet}>
        <SheetContent className="min-w-[80vw] max-h-screen overflow-hidden">
          <div className="h-full flex flex-col">
            <SheetHeader className="pb-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <SheetTitle className="text-xl font-bold text-gray-800">
                    CVE Comparison Analysis
                  </SheetTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Complete vulnerability assessment data for all assets
                  </p>
                </div>
              </div>

              {/* Summary Stats */}
              <div className="flex space-x-6 mt-4 pt-4 border-t border-gray-100">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {allAssetCves?.length || 0}
                  </div>
                  <div className="text-xs text-gray-500">Total Comparisons</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {allAssetCves?.filter((cve) => cve.isVulnerability)
                      .length || 0}
                  </div>
                  <div className="text-xs text-gray-500">Vulnerabilities</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {allAssetCves?.filter((cve) => !cve.isVulnerability)
                      .length || 0}
                  </div>
                  <div className="text-xs text-gray-500">Safe Assets</div>
                </div>
              </div>
            </SheetHeader>

            <ScrollArea className="flex-1 mt-4">
              {allAssetCves && allAssetCves.length > 0 ? (
                <Accordion type="single" collapsible className="space-y-4">
                  {allAssetCves.map((cveData: any, index: number) => (
                    <AccordionItem
                      key={cveData._id || index}
                      value={String(cveData._id || index)}
                      className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
                    >
                      <AccordionTrigger className="px-6 py-4 hover:bg-gray-50 [&[data-state=open]]:bg-blue-50 transition-colors duration-200">
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center space-x-4">
                            {/* Status Indicator */}
                            <div
                              className={`w-4 h-4 rounded-full ${
                                cveData.isVulnerability
                                  ? "bg-red-500"
                                  : "bg-green-500"
                              }`}
                            />

                            {/* CVE Info */}
                            <div className="text-left">
                              <div className="font-semibold text-gray-800">
                                {cveData.nvdData?.cveId || "Unknown CVE"}
                              </div>
                              <div className="text-sm text-gray-500">
                                Asset: {cveData.asset || "N/A"}
                              </div>
                            </div>
                          </div>

                          {/* Quick Stats */}
                          <div className="flex items-center space-x-6 text-sm">
                            <div className="text-center">
                              <div className="font-bold text-orange-600">
                                {cveData.nvdData?.cvssScore || "N/A"}
                              </div>
                              <div className="text-xs text-gray-500">
                                CVSS Score
                              </div>
                            </div>
                            <div className="text-center">
                              <div
                                className={`font-bold ${
                                  cveData.isVulnerability
                                    ? "text-red-600"
                                    : "text-green-600"
                                }`}
                              >
                                {cveData.isVulnerability
                                  ? "VULNERABLE"
                                  : "SAFE"}
                              </div>
                              <div className="text-xs text-gray-500">
                                Status
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="font-bold text-blue-600">
                                {cveData.nvdData?.calculatorData?.deltaScore ||
                                  "N/A"}
                              </div>
                              <div className="text-xs text-gray-500">
                                Risk Reduction
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                          {/* Basic Information */}
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
                            <h4 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                              <FileText className="w-5 h-5 mr-2" />
                              Basic Information
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-white p-3 rounded-lg border">
                                <span className="text-sm font-medium text-gray-600">
                                  Asset:
                                </span>
                                <p className="text-sm mt-1 font-semibold">
                                  {cveData.asset || "N/A"}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border">
                                <span className="text-sm font-medium text-gray-600">
                                  Assessment ID:
                                </span>
                                <p className="text-xs mt-1 font-mono text-gray-500">
                                  {String(cveData.assessment || "N/A")}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border">
                                <span className="text-sm font-medium text-gray-600">
                                  Created:
                                </span>
                                <p className="text-sm mt-1">
                                  {cveData.createdAt
                                    ? format(
                                        new Date(cveData.createdAt),
                                        "MMM d, yyyy HH:mm"
                                      )
                                    : "N/A"}
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* CVE Details */}
                          <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-4 border border-red-200">
                            <h4 className="text-lg font-semibold text-red-800 mb-3 flex items-center">
                              <AlertTriangle className="w-5 h-5 mr-2" />
                              CVE Details
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-3">
                                <div className="bg-white p-3 rounded-lg border">
                                  <span className="text-sm font-medium text-red-700">
                                    CVE ID:
                                  </span>
                                  <p className="text-sm mt-1 font-mono">
                                    {cveData.nvdData?.cveId || "N/A"}
                                  </p>
                                </div>
                                <div className="bg-white p-3 rounded-lg border">
                                  <span className="text-sm font-medium text-red-700">
                                    CVSS Score:
                                  </span>
                                  <p className="text-lg mt-1 font-bold text-orange-600">
                                    {cveData.nvdData?.cvssScore || "N/A"}
                                  </p>
                                </div>
                              </div>
                              <div className="space-y-3">
                                <div className="bg-white p-3 rounded-lg border">
                                  <span className="text-sm font-medium text-red-700">
                                    Published:
                                  </span>
                                  <p className="text-sm mt-1">
                                    {cveData.nvdData?.published || "N/A"}
                                  </p>
                                </div>
                                <div className="bg-white p-3 rounded-lg border">
                                  <span className="text-sm font-medium text-red-700">
                                    Source:
                                  </span>
                                  <p className="text-sm mt-1">
                                    {cveData.nvdData?.source || "N/A"}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* CVE Description */}
                            {cveData.nvdData?.description && (
                              <div className="mt-4 bg-white p-4 rounded-lg border">
                                <span className="text-sm font-medium text-red-700">
                                  Description:
                                </span>
                                <p className="text-sm mt-2 leading-relaxed text-gray-700">
                                  {cveData.nvdData.description}
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Risk Calculator Results */}
                          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
                            <h4 className="text-lg font-semibold text-green-800 mb-3 flex items-center">
                              <BarChart3 className="w-5 h-5 mr-2" />
                              Risk Calculator Results
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Base Score
                                </span>
                                <p className="text-xl mt-1 font-bold text-blue-600">
                                  {cveData.nvdData?.calculatorData?.baseScore ||
                                    "N/A"}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Risk Before
                                </span>
                                <p className="text-xl mt-1 font-bold text-red-500">
                                  {cveData.nvdData?.calculatorData
                                    ?.envScoreBefore || "N/A"}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Risk After
                                </span>
                                <p className="text-xl mt-1 font-bold text-green-500">
                                  {cveData.nvdData?.calculatorData
                                    ?.envScoreAfter || "N/A"}
                                </p>
                              </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Delta Score
                                </span>
                                <p className="text-xl mt-1 font-bold text-purple-600">
                                  {cveData.nvdData?.calculatorData
                                    ?.deltaScore || "N/A"}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Risk Level Before
                                </span>
                                <p className="text-sm mt-1 font-semibold">
                                  {cveData.nvdData?.calculatorData
                                    ?.riskLevelBefore || "N/A"}
                                </p>
                              </div>
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-green-700">
                                  Risk Level After
                                </span>
                                <p className="text-sm mt-1 font-semibold">
                                  {cveData.nvdData?.calculatorData
                                    ?.riskLevelAfter || "N/A"}
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Analysis Comments */}
                          {cveData.comments && (
                            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
                              <h4 className="text-lg font-semibold text-purple-800 mb-3 flex items-center">
                                <MessageSquare className="w-5 h-5 mr-2" />
                                Analysis Comments
                              </h4>
                              <div className="bg-white p-4 rounded-lg border">
                                <p className="text-sm leading-relaxed text-gray-700">
                                  {cveData.comments}
                                </p>
                              </div>
                            </div>
                          )}

                          {/* Status and Metadata */}
                          <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-4 border border-yellow-200">
                            <h4 className="text-lg font-semibold text-yellow-800 mb-3 flex items-center">
                              <Zap className="w-5 h-5 mr-2" />
                              Status & Metadata
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div
                                className={`p-3 rounded-lg border text-center ${
                                  cveData.isVulnerability
                                    ? "bg-red-100 border-red-300"
                                    : "bg-green-100 border-green-300"
                                }`}
                              >
                                <span className="text-sm font-medium text-gray-700">
                                  Vulnerability Status
                                </span>
                                <div
                                  className={`text-lg mt-1 font-bold flex items-center justify-center space-x-2 ${
                                    cveData.isVulnerability
                                      ? "text-red-800"
                                      : "text-green-800"
                                  }`}
                                >
                                  {cveData.isVulnerability ? (
                                    <>
                                      <AlertTriangle className="w-5 h-5" />
                                      <span>VULNERABLE</span>
                                    </>
                                  ) : (
                                    <>
                                      <ShieldCheck className="w-5 h-5" />
                                      <span>SAFE</span>
                                    </>
                                  )}
                                </div>
                              </div>
                              <div className="bg-white p-3 rounded-lg border text-center">
                                <span className="text-sm font-medium text-gray-700">
                                  Comparison ID
                                </span>
                                <p className="text-xs mt-1 font-mono text-gray-500">
                                  {String(cveData._id || "N/A")}
                                </p>
                              </div>
                            </div>
                            {(cveData.createdAt || cveData.updatedAt) && (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                {cveData.createdAt && (
                                  <div className="bg-white p-3 rounded-lg border">
                                    <span className="text-sm font-medium text-gray-700">
                                      Created At:
                                    </span>
                                    <p className="text-sm mt-1">
                                      {format(
                                        new Date(cveData.createdAt),
                                        "PPP 'at' p"
                                      )}
                                    </p>
                                  </div>
                                )}
                                {cveData.updatedAt && (
                                  <div className="bg-white p-3 rounded-lg border">
                                    <span className="text-sm font-medium text-gray-700">
                                      Last Updated:
                                    </span>
                                    <p className="text-sm mt-1">
                                      {format(
                                        new Date(cveData.updatedAt),
                                        "PPP 'at' p"
                                      )}
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>

                          {/* Raw Data Section */}
                          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-4 border border-gray-200">
                            <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                              <span className="mr-2">🔧</span>
                              Complete Raw Data
                            </h4>
                            <details className="cursor-pointer">
                              <summary className="text-sm font-medium text-gray-600 hover:text-gray-800 mb-2">
                                Click to view complete JSON data
                              </summary>
                              <pre className="text-xs bg-gray-100 p-4 rounded-lg border overflow-x-auto max-h-64 overflow-y-auto">
                                {JSON.stringify(cveData, null, 2)}
                              </pre>
                            </details>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              ) : (
                <div className="text-center py-12">
                  <div className="flex justify-center mb-4">
                    <Shield className="w-16 h-16 text-gray-400 opacity-50" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">
                    No CVE Data Available
                  </h3>
                  <p className="text-gray-500">
                    No vulnerability comparison data has been saved for this
                    assessment yet.
                  </p>
                </div>
              )}
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>
      <Sheet open={showHistorySheet} onOpenChange={setShowHistorySheet}>
        <SheetContent className="min-w-[70vw]">
          <ScrollArea className="ph">
            <SheetHeader>
              <SheetTitle>Submission history</SheetTitle>
            </SheetHeader>

            <div>
              <Accordion type="single" collapsible>
                {(filterHistory || []).map((history, idx) => {
                  return (
                    <AccordionItem
                      value={String(history._id)}
                      key={String(history._id)}
                    >
                      <AccordionTrigger>
                        {format(history?.createdAt, "MMM d,yyyy - h:mm a")}
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead> Sender</TableHead>
                              <TableCell>
                                {history?.actionTaker?.email}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Reciepient</TableHead>

                              <TableCell>
                                {history?.actionReciver?.email}
                              </TableCell>
                            </TableRow>

                            <TableRow>
                              <TableHead>Vendor</TableHead>
                              <TableCell>{history.vendor?.company}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory</TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot?.submission
                                    ?.question?.input.label
                                }
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory Details</TableHead>
                              <TableCell>
                                {history.submissionSnapshot.submission.answer}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.cvssScore
                                }
                              </TableCell>
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.score
                                }
                              </TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {history.submissionSnapshot?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Created Date :
                              </TableHead>
                              <TableCell>
                                {/* @ts-ignore */}
                                {history.submissionSnapshot.vulnerability
                                  ?.createdAt
                                  ? format(
                                      // @ts-ignore
                                      history.submissionSnapshot.vulnerability
                                        ?.createdAt,
                                      "MMM-d-yyyy - h:mm a"
                                    )
                                  : "-"}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Vulnerability Description</TableHead>
                              <TableCell>
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      history?.submissionSnapshot?.vulnerability
                                        ?.remarks ?? "-",
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance by Vendor:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot
                                  ?.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.ACCEPT
                                  ? "Vendor acknowledged for remediation(s)"
                                  : history.submissionSnapshot
                                      ?.vendorAcceptanceStatus ===
                                    VendorAcceptanceStatus.REJECT
                                  ? " Vendor identified false positive"
                                  : null}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance Description:
                              </TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot
                                    ?.vendorRejectReason
                                }
                              </TableCell>
                            </TableRow>
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.RESOLVED ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Resolved Date:
                                  </TableHead>
                                  <TableCell>
                                    {history?.submissionSnapshot?.vulnerability
                                      ?.resolvedDate
                                      ? format(
                                          history?.submissionSnapshot
                                            ?.vulnerability?.resolvedDate,
                                          "MMM - d -yyyy - h:mm a"
                                        )
                                      : null}
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : (
                              ""
                            )}
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.EXCEPTION ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vendor Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.resolveDescription
                                    }
                                  </TableCell>
                                </TableRow>
                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow> */}

                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Evidence:
                                  </TableHead>
                                  <TableCell>
                                    <Button>
                                      <Download
                                        onClick={() =>
                                          download({
                                            fileId: history?.submissionSnapshot
                                              .vulnerability
                                              ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                          })
                                        }
                                      />
                                    </Button>
                                  </TableCell>
                                </TableRow> */}
                              </>
                            ) : null}

                            {history?.submissionSnapshot.vulnerability
                              ?.vulnerabilityClosureDescriptionBySoc ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Closer Description By Soc:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityClosureDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : null}
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Closure status by Customer:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot.vulnerability
                                  ?.vulnerabilityClosureCompanyApprovalStatus ===
                                VulnerabilityClosureStatus.CLOSED
                                  ? "Approved for closure"
                                  : history?.submissionSnapshot.vulnerability
                                      ?.vulnerabilityClosureCompanyApprovalStatus ===
                                    VulnerabilityClosureStatus.OPEN
                                  ? "Required reassessment"
                                  : "Pending"}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
      {/* Auditor Form */}
      {/* <Dialog open={auditorDialog} onOpenChange={setAuditorDialog}>
        <DialogContent>
          <AuditorScoreForm
            methods={auditorMethods}
            handleSubmit={handleAuditorSubmit}
          />
        </DialogContent>
      </Dialog> */}
      {/* Exception conformation */}

      {/* Vulnerability Closer */}

      <div className=" rounded-2xl shadow-xl overflow-auto p-10">
        <Table>
          <TableHeader className="sticky top-0 z-10">
            <TableRow className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 border-none hover:bg-gradient-to-r hover:from-slate-700 hover:via-slate-600 hover:to-slate-700">
              {userRole === USER_ROLE.SOC_ANALYST && (
                <>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Building2 className="w-4 h-4" />
                      <span>Asset</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Shield className="w-4 h-4" />
                      <span>Vulnerability Status</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <MessageSquare className="w-4 h-4" />
                      <span>Vendor Response</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Zap className="w-4 h-4" />
                      <span>VSOC Resolve/Exception</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>Customer Close Status</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Flag className="w-4 h-4 text-red-300" />
                      <span>VSOC Close Status</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <FileText className="w-4 h-4" />
                      <span>History</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <Search className="w-4 h-4" />
                      <span>CVE</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3">
                    <div className="flex items-center justify-center space-x-2">
                      <BarChart3 className="w-4 h-4" />
                      <span>CVE ALL</span>
                    </div>
                  </TableHead>
                </>
              )}
              {userRole === USER_ROLE.AUDITOR && (
                <>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3 border-r border-slate-600">
                    <div className="flex items-center justify-center space-x-2">
                      <FileText className="w-4 h-4" />
                      <span>Status</span>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-sm text-white text-center py-4 px-3">
                    <div className="flex items-center justify-center space-x-2">
                      <Zap className="w-4 h-4" />
                      <span>Action</span>
                    </div>
                  </TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody className="overflow-y-auto">
            {(assessmentSubmissions.data || [])
              .filter((isb) =>
                !questions.length
                  ? true
                  : questions.includes(String(isb.submission.question?._id))
              )
              .map((submission) => {
                const question = submission.submission.question;
                if (!question) return null;

                const getStatusColor = (status: any) => {
                  if (status === VulnerabilityClosureStatus.OPEN) {
                    return "bg-red-50 border-red-200";
                  }
                  return "bg-green-50 border-green-200";
                };

                return (
                  <TableRow
                    key={String(submission._id)}
                    className={`border-b border-gray-100 hover:bg-gray-50 transition-all duration-200 group ${getStatusColor(
                      submission.vulnerability?.vulnerabilityClosureStatus
                    )}`}
                  >
                    <TableCell className="py-4 px-3 border-r border-gray-100">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="max-w-sm">
                            <div className="text-sm font-medium text-gray-800 line-clamp-2">
                              {submission.submission.answer}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              Asset ID: {String(submission._id).slice(-6)}
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => {
                            setExceptionDialog(true);
                            // @ts-expect-error
                            methods.setValue("_id", submission?._id);
                          }}
                          size="sm"
                          className="bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 hover:border-gray-400 transition-all duration-200 rounded-lg"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>

                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <div className="flex justify-center">
                        <VulnerabilityDetailsSheet
                          submission={submission}
                          role={roles}
                          onVulnerabilityPress={
                            submission.sectionType === SECTIONTYPE.INVENTORY
                              ? onSocModalOpen
                              : onAuditorModalOpen
                          }
                          onUpdateVulnerabilityPress={
                            handleVulnerabilityUpdateRating
                          }
                          onAssetSelect={(data) => setAsset(data)}
                        />
                      </div>
                    </TableCell>

                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <div className="flex justify-center">
                        <VulnerabilityResponseSheet
                          role={roles}
                          submission={submission}
                          onUpdateVulnerabilityPress={
                            handleVulnerabilityUpdateRating
                          }
                        />
                      </div>
                    </TableCell>

                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <div className="flex justify-center">
                        <VulnerabilityResolveAndExceptionSheet
                          role={roles}
                          submission={submission}
                          onUpdateVulnerabilityPress={
                            handleVulnerabilityUpdateRating
                          }
                        />
                      </div>
                    </TableCell>
                    {/* Exception */}
                    {/* <TableCell>
                    <Button onClick={() => setExceptionDialog(true)}>
                      <Plus />
                      Exception
                    </Button>
                  </TableCell> */}
                    {/* Closure mail*/}
                    {/* <TableCell>
                    <Button onClick={() => setClosureDialog(true)}>
                      <Plus />
                      Request Closure
                    </Button>
                  </TableCell> */}
                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <div className="flex justify-center">
                        <VulnerabilityCloseSheet
                          role={roles}
                          submission={submission}
                        />
                      </div>
                    </TableCell>

                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <div className="flex justify-center items-center">
                        {submission.vulnerability
                          ?.vulnerabilityClosureStatus ===
                        VulnerabilityClosureStatus.OPEN ? (
                          <div className="flex flex-col items-center space-y-1">
                            <Flag size={24} className="text-red-500" />
                            <span className="text-xs font-medium text-red-600 bg-red-100 px-2 py-1 rounded-full">
                              OPEN
                            </span>
                          </div>
                        ) : submission.vulnerability
                            ?.vulnerabilityClosureStatus ===
                          VulnerabilityClosureStatus.CLOSED ? (
                          <div className="flex flex-col items-center space-y-1">
                            <CheckCheck size={24} className="text-green-500" />
                            <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                              CLOSED
                            </span>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center space-y-1">
                            <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                            <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              PENDING
                            </span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <Button
                        onClick={() =>
                          showHistory(
                            submission.submission.question?._id as string,
                            submission.submission.answer as string
                          )
                        }
                        className="bg-blue-100 hover:bg-blue-200 text-blue-700 border border-blue-300 hover:border-blue-400 transition-all duration-200 rounded-lg px-3 py-2 text-sm font-medium"
                      >
                        <div className="flex items-center space-x-1">
                          <FileText className="w-4 h-4" />
                          <span>History</span>
                        </div>
                      </Button>
                    </TableCell>

                    <TableCell className="py-4 px-3 border-r border-gray-100 text-center">
                      <Button
                        onClick={() =>
                          handleRoute(submission.submission.answer as string)
                        }
                        className="bg-purple-100 hover:bg-purple-200 text-purple-700 border border-purple-300 hover:border-purple-400 transition-all duration-200 rounded-lg px-3 py-2 text-sm font-medium"
                      >
                        <div className="flex items-center space-x-1">
                          <Search className="w-4 h-4" />
                          <span>CVE</span>
                        </div>
                      </Button>
                    </TableCell>

                    <TableCell className="py-4 px-3 text-center">
                      <Button
                        onClick={() => {
                          setShowCveSheet(true);
                          setAsset(submission.submission.answer as string);
                        }}
                        className="bg-green-100 hover:bg-green-200 text-green-700 border border-green-300 hover:border-green-400 transition-all duration-200 rounded-lg px-3 py-2 text-sm font-medium group-hover:scale-105"
                      >
                        <div className="flex items-center space-x-1">
                          <BarChart3 className="w-4 h-4" />
                          <span>CVE ALL</span>
                          <span className="bg-green-200 text-green-800 px-1.5 py-0.5 rounded-full text-xs font-bold">
                            {allAssetCves?.length || 0}
                          </span>
                        </div>
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
          {(isSoc || isLeadSoc || isAuditor) && (
            <TableFooter className="sticky bottom-0 bg-slate-200">
              <TableCell colSpan={2}></TableCell>
              <TableCell className="text-right">
                Send All Vulnerabilities:
              </TableCell>
              <TableCell colSpan={7}>
                {/* Send Email Trigger */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>
                      <Mail className="size-4 mr-2" />
                      Send Email To All
                    </Button>
                  </DialogTrigger>

                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Send Score Card</DialogTitle>
                    </DialogHeader>

                    <form onSubmit={handleSendVCardMail}>
                      <FormProvider {...vScoreFormMethods}>
                        <FormTextField
                          name="message"
                          label="Remarks"
                          placeholder="Write any optional remarks"
                        />
                        <div className="flex gap-4 mt-4 justify-end">
                          <DialogClose asChild>
                            <Button type="button" variant="destructive">
                              Close
                            </Button>
                          </DialogClose>
                          <Button>Send</Button>
                        </div>
                      </FormProvider>
                    </form>
                  </DialogContent>
                </Dialog>
              </TableCell>
            </TableFooter>
          )}
        </Table>
      </div>
    </div>
  );
}
