"use client";
import React, { useMemo, useState } from "react";
import { useRole } from "@/hooks/useRole";
import { useParams, useSearchParams } from "next/navigation";
import { trpc } from "@/providers/Providers";
import _ from "lodash";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  CompanyExceptionApproval,
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from "../../../../../shared/types/AssessmentSubmission";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CRITICALITY_LEVELS } from "../../../../../shared/types/Company";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetTrigger,
} from "@/components/ui/sheet";
import { differenceInDays, format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { CheckCheck, Download, Eye, Flag, Minus } from "lucide-react";
import { useDownload } from "@/hooks/useDownload";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import CompanyClosureApprovalForm from "@/elements/assessments/company-closure-approval.form";
import CompanyExceptionApprovalForm from "@/elements/assessments/company-exception-approval.form";

export default function CompanyAssessmentApprovals() {
  const roles = useRole();

  const [companyExceptionOrResolveDialog, setCompanyExceptionOrResolveDialog] =
    useState(false);
  const [companyClosureDialog, setCompanyClosureDialog] = useState(false);
  const [companyClosureDialogForReject, setCompanyClosureDialogForReject] =
    useState(false);
  const download = useDownload();
  const { assessment } = useParams<{ assessment: string }>();
  const _assessment = trpc.assessment.getAssessment.useQuery({ assessment });
  const params = useSearchParams();
  const questions = params.getAll("question");

  const questionsFilter = useMemo(() => {
    return _.groupBy(
      (_assessment.data?.assignments || []).map((_) => _.questions).flat(),
      "section.sectionType"
    );
  }, [_assessment.data?.assignments]);

  const generalQuestions = useMemo(
    () => questionsFilter?.general?.map((_) => String(_?._id)),
    [questionsFilter.general]
  );
  const inventoryQuestions = useMemo(
    () => questionsFilter?.inventory?.map((_) => String(_?._id)),
    [questionsFilter.inventory]
  );

  const generalQuestionSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment, questions: generalQuestions },
      {
        enabled: !!generalQuestions?.length,
      }
    );

  const inventoryQuestionSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment, questions: inventoryQuestions },
      {
        enabled: !!inventoryQuestions?.length,
      }
    );

  console.log(inventoryQuestionSubmissions.data);

  return (
    <div className="space-y-4 container1">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Sno</TableHead>
            <TableHead>Vendor</TableHead>
            <TableHead>Inventory</TableHead>
            <TableHead>Vulnerability</TableHead>
            <TableHead className="w-2">
              Vulnerability Status Acknowledged/False Positive by vendor
            </TableHead>
            <TableHead>Vendor Vulnerability Resolve/Exception Status</TableHead>
            <TableHead>Approval Status</TableHead>
            <TableHead>Vulnerability Closed Status</TableHead>
            <TableHead>Check Comparasion</TableHead>
            <TableHead>Details</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {(inventoryQuestionSubmissions.data || [])
            .filter((isb) =>
              !questions.length
                ? true
                : questions.includes(String(isb.submission.question?._id))
            )
            .map((isb, idx) => {
              const resolvedDate = isb?.vulnerability?.resolvedDate;
              // @ts-ignore
              const createdAt = isb?.vulnerability?.createdAt;

              const resolvedDateFormatted = resolvedDate
                ? format(resolvedDate, "MMM d, yyyy")
                : "-";
              const createdAtFormatted = createdAt
                ? format(createdAt, "MMM d, yyyy")
                : "-";

              const daysDifference =
                resolvedDate && createdAt
                  ? differenceInDays(resolvedDate, createdAt)
                  : null;

              return (
                <TableRow key={String(isb._id)}>
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell>{isb.vendor?.company}</TableCell>
                  <TableCell>{isb.submission.question?.input.label}</TableCell>
                  <TableCell>
                    {isb.vulnerability?.criticalityLevel ? (
                      // VULNERABILITY SHEET
                      <Sheet>
                        <SheetTrigger>
                          <Badge
                            className={cn(
                              "font-bold w-32 py-[10px] ",
                              isb.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.HIGH &&
                                "bg-rose-600 pl-12 hover:bg-rose-400",
                              isb.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.CRITICAL &&
                                "bg-red-700 pl-8 hover:bg-red-500",
                              isb.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.MEDIUM &&
                                "bg-orange-600 pl-9 hover:bg-orange-400",
                              isb.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.LOW &&
                                "bg-yellow-600 pl-12 hover:bg-yellow-400",
                              isb.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.VERYLOW &&
                                "bg-green-700 pl-8"
                            )}
                          >
                            {/* @ts-ignore */}
                            {isb.vulnerability?.criticalityLevel?.toUpperCase() ??
                              "PENDING"}
                          </Badge>
                        </SheetTrigger>
                        <SheetContent className="min-w-[1500px] overflow-y-auto">
                          <SheetDescription>
                            Vulnerability {isb.vulnerability?.resolveStatus}{" "}
                            Details
                          </SheetDescription>

                          <Table>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Inventory
                              </TableHead>
                              <TableCell>
                                {isb?.submission?.question?.input.label}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Inventory Details
                              </TableHead>
                              <TableCell>{isb.submission.answer}</TableCell>
                            </TableRow>
                          </Table>
                          <Table>
                            <TableHeader>
                              <TableRow className="text-center">
                                <TableHead className="text-black font-semibold  ">
                                  Cvss Score
                                </TableHead>
                                <TableHead className="text-black font-semibold">
                                  VendorAi Score
                                </TableHead>
                                <TableHead className="text-black font-semibold">
                                  Criticality
                                </TableHead>
                                <TableHead className="text-black font-semibold">
                                  MSA/SOW Remediation Timelines
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              <TableRow className="text-center">
                                <TableCell>
                                  {isb.vulnerability?.cvssScore}
                                </TableCell>
                                <TableCell>
                                  {isb.vulnerability?.score}
                                </TableCell>
                                <TableCell
                                  className={cn(
                                    "font-bold",
                                    // @ts-ignore
                                    isb?.vulnerability?.criticalityLevel ===
                                      CRITICALITY_LEVELS.HIGH &&
                                      "text-rose-600 w",
                                    // @ts-ignore
                                    isb?.vulnerability?.criticalityLevel ===
                                      CRITICALITY_LEVELS.CRITICAL &&
                                      "text-red-700",
                                    // @ts-ignore
                                    isb?.vulnerability?.criticalityLevel ===
                                      CRITICALITY_LEVELS.MEDIUM &&
                                      "text-orange-600",
                                    // @ts-ignore
                                    isb?.vulnerability?.criticalityLevel ===
                                      CRITICALITY_LEVELS.LOW &&
                                      "text-yellow-600",
                                    // @ts-ignore
                                    isb?.vulnerability?.criticalityLevel ===
                                      CRITICALITY_LEVELS.VERYLOW &&
                                      "text-green-700"
                                  )}
                                >
                                  {isb?.vulnerability?.criticalityLevel.toUpperCase()}
                                </TableCell>
                                <TableCell>
                                  {isb.vendor?.criticalityLevels?.map(
                                    (c, idx) => (
                                      <div key={idx}>
                                        {isb.vulnerability?.criticalityLevel ===
                                        c.level
                                          ? `${c.timeDuration} Days`
                                          : ""}
                                      </div>
                                    )
                                  )}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                          <Table>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Created Date :
                              </TableHead>
                              <TableCell>
                                {/* @ts-ignore */}
                                {isb.vulnerability?.createdAt
                                  ? format(
                                      // @ts-ignore
                                      isb.vulnerability?.createdAt,
                                      "MMM-d-yyyy"
                                    )
                                  : "-"}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Description:
                              </TableHead>
                              <TableCell className="w-[1000px]">
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: isb.vulnerability
                                      ?.remarks as string,
                                  }}
                                  className="text-lg"
                                />
                              </TableCell>
                            </TableRow>
                          </Table>
                        </SheetContent>
                      </Sheet>
                    ) : (
                      <Minus size={30} className="ml-12" />
                    )}
                  </TableCell>
                  <TableCell>
                    {/* Vulnerability Accepted/Rejected by vendor */}
                    <Sheet>
                      <SheetTrigger>
                        {isb.vendorAcceptanceStatus ? (
                          <Badge
                            className={cn(
                              ` font-bold w-32 py-[10px] pl-2  ${
                                isb.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.REJECT
                                  ? "bg-red-500"
                                  : null
                              } `
                            )}
                          >
                            {isb.vendorAcceptanceStatus ===
                            VendorAcceptanceStatus.ACCEPT
                              ? "ACKNOWLEDGED"
                              : isb.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.REJECT
                              ? "FALSE POSITIVE"
                              : null}
                          </Badge>
                        ) : (
                          <Minus size={30} className="ml-12" />
                        )}
                      </SheetTrigger>
                      <SheetContent className="min-w-[1500px] overflow-y-auto">
                        <SheetDescription>
                          Vulnerability {isb.vendorAcceptanceStatus} Details
                        </SheetDescription>

                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory
                            </TableHead>
                            <TableCell>
                              {isb?.submission?.question?.input.label}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory Details
                            </TableHead>
                            <TableCell>{isb.submission.answer}</TableCell>
                          </TableRow>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                MSA/SOW Remediation Timelines
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {isb.vulnerability?.cvssScore}
                              </TableCell>
                              <TableCell>{isb.vulnerability?.score}</TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {isb?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                              <TableCell>
                                {isb.vendor?.criticalityLevels?.map(
                                  (c, idx) => (
                                    <div key={idx}>
                                      {isb.vulnerability?.criticalityLevel ===
                                      c.level
                                        ? `${c.timeDuration} Days`
                                        : ""}
                                    </div>
                                  )
                                )}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Created Date :
                            </TableHead>
                            <TableCell>
                              {/* @ts-ignore */}
                              {isb.vulnerability?.createdAt
                                ? format(
                                    // @ts-ignore
                                    isb.vulnerability?.createdAt,
                                    "MMM-d-yyyy"
                                  )
                                : "-"}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Description:
                            </TableHead>
                            <TableCell className="w-[1000px]">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: isb.vulnerability?.remarks as string,
                                }}
                                className="text-lg"
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Acceptance by Vendor:
                            </TableHead>
                            <TableCell>
                              {isb.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.ACCEPT
                                ? "Vendor acknowledged for remediation(s)"
                                : isb.vendorAcceptanceStatus ===
                                  VendorAcceptanceStatus.REJECT
                                ? " Vendor identified false positive"
                                : null}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Acceptance Description:
                            </TableHead>
                            <TableCell>{isb?.vendorRejectReason}</TableCell>
                          </TableRow>
                          {isb.vulnerability
                            ?.vulnerabilityClosureDescriptionBySoc ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Description By Soc:
                                </TableHead>
                                <TableCell>
                                  {
                                    isb.vulnerability
                                      ?.vulnerabilityClosureDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>

                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Evidence By Soc:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityClosureEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}
                        </Table>
                        <Dialog
                          open={companyClosureDialogForReject}
                          onOpenChange={setCompanyClosureDialogForReject}
                        >
                          <DialogContent className="min-w-[1000px]">
                            <DialogHeader className="text-xl font-bold pl-40 underline">
                              Customer Vulnerability Closure Approval Request
                            </DialogHeader>
                            <CompanyClosureApprovalForm
                              submissionId={String(isb._id)}
                            />
                          </DialogContent>
                        </Dialog>
                        <SheetFooter className="mt-10 items-center">
                          {isb.vendorAcceptanceStatus ===
                            VendorAcceptanceStatus.REJECT &&
                          isb.vulnerability
                            ?.vulnerabilityClosureDescriptionBySoc ? (
                            <Button
                              disabled={
                                !isb.vulnerability
                                  ?.vulnerabilityClosureDescriptionBySoc
                              }
                              onClick={() => {
                                setCompanyClosureDialogForReject(true);
                              }}
                            >
                              CLose Vulnerability
                            </Button>
                          ) : null}
                        </SheetFooter>
                      </SheetContent>
                    </Sheet>
                  </TableCell>
                  <TableCell>
                    {/* Inventory details */}
                    <Sheet>
                      <SheetTrigger
                        disabled={!isb.vulnerability?.resolveStatus?.length}
                      >
                        {isb.vulnerability?.resolveStatus ===
                        VulnerabilityResolveStatus.EXCEPTION ? (
                          <Badge
                            className={cn(
                              `py-2 w-32 pl-7 ${
                                // @ts-ignore
                                isb?.vulnerability?.resolveStatus ===
                                VulnerabilityResolveStatus.RESOLVED
                                  ? "bg-green-800"
                                  : isb?.vulnerability?.resolveStatus ===
                                    VulnerabilityResolveStatus.EXCEPTION
                                  ? "bg-orange-600"
                                  : "bg-red-500"
                              }`
                            )}
                          >
                            EXCEPTION
                          </Badge>
                        ) : isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.RESOLVED ? (
                          <Badge
                            className={cn(
                              `py-2 w-32 pl-7 ${
                                isb?.vulnerability?.resolveStatus ===
                                VulnerabilityResolveStatus.RESOLVED
                                  ? "bg-green-600"
                                  : isb?.vulnerability?.resolveStatus ===
                                    VulnerabilityResolveStatus.EXCEPTION
                                  ? "bg-orange-600"
                                  : "bg-red-500"
                              }`
                            )}
                          >
                            RESOLVED
                          </Badge>
                        ) : (
                          <Minus size={30} className="ml-12" />
                        )}
                      </SheetTrigger>
                      <SheetContent className="min-w-[1500px] overflow-y-auto">
                        <SheetDescription>
                          Vulnerability {isb.vulnerability?.resolveStatus}{" "}
                          Details
                        </SheetDescription>

                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory
                            </TableHead>
                            <TableCell>
                              {isb?.submission?.question?.input.label}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory Details
                            </TableHead>
                            <TableCell>{isb.submission.answer}</TableCell>
                          </TableRow>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                MSA/SOW Remediation Timelines
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {isb.vulnerability?.cvssScore}
                              </TableCell>
                              <TableCell>{isb.vulnerability?.score}</TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {isb?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                              <TableCell>
                                {isb.vendor?.criticalityLevels?.map(
                                  (c, idx) => (
                                    <div key={idx}>
                                      {isb.vulnerability?.criticalityLevel ===
                                      c.level
                                        ? `${c.timeDuration} Days`
                                        : ""}
                                    </div>
                                  )
                                )}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Created Date :
                            </TableHead>
                            <TableCell>
                              {/* @ts-ignore */}
                              {isb.vulnerability?.createdAt
                                ? format(
                                    // @ts-ignore
                                    isb.vulnerability?.createdAt,
                                    "MMM-d-yyyy"
                                  )
                                : "-"}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Description:
                            </TableHead>
                            <TableCell className="w-[1000px]">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: isb.vulnerability?.remarks as string,
                                }}
                                className="text-lg"
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Acceptance by Vendor:
                            </TableHead>
                            <TableCell>
                              {isb.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.ACCEPT
                                ? "Vendor acknowledged for remediation(s)"
                                : isb.vendorAcceptanceStatus ===
                                  VendorAcceptanceStatus.REJECT
                                ? " Vendor identified false positive"
                                : null}
                            </TableCell>
                          </TableRow>
                          {isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.RESOLVED ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Resolved Date:
                                </TableHead>
                                <TableCell>
                                  {isb?.vulnerability?.resolvedDate
                                    ? format(
                                        isb?.vulnerability?.resolvedDate,
                                        "MMM - d -yyyy"
                                      )
                                    : null}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Days Vulnerability Resolved:
                                </TableHead>
                                <TableCell>
                                  {daysDifference ?? "-"} Days
                                </TableCell>
                              </TableRow>
                            </>
                          ) : (
                            ""
                          )}
                          {isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.EXCEPTION ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vendor Exception Reason:
                                </TableHead>
                                <TableCell>
                                  {isb.vulnerability?.resolveDescription}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Soc Exception Reason:
                                </TableHead>
                                <TableCell>
                                  {
                                    isb.vulnerability
                                      ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vendor Exception Evidence:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityResolvedOrExceptionEvidence as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Soc Exception Evidence:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}
                          {isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.RESOLVED ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Resolved Status by vendor:
                                </TableHead>
                                <TableCell>
                                  {isb.vulnerability?.resolveStatus}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Description By Soc:
                                </TableHead>
                                <TableCell>
                                  {
                                    isb.vulnerability
                                      ?.vulnerabilityClosureDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Resolved Evidence By Vendor:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityResolvedOrExceptionEvidence as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Evidence By Soc:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityClosureEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}
                        </Table>
                        <SheetFooter className="mt-10 items-center">
                          <Dialog
                            open={companyExceptionOrResolveDialog}
                            onOpenChange={setCompanyExceptionOrResolveDialog}
                          >
                            <DialogContent className="min-w-[1000px]">
                              <CompanyExceptionApprovalForm
                                submissionId={String(isb._id)}
                              />
                            </DialogContent>
                          </Dialog>
                          <Dialog
                            open={companyClosureDialog}
                            onOpenChange={setCompanyClosureDialog}
                          >
                            <DialogContent className="min-w-[1000px]">
                              <DialogHeader className="text-xl font-bold pl-40 underline">
                                Customer Vulnerability Closure Approval Request
                              </DialogHeader>
                              <CompanyClosureApprovalForm
                                submissionId={String(isb._id)}
                              />
                            </DialogContent>
                          </Dialog>
                          {isb.vulnerability
                            ?.vulnerabilityExceptionApprovalDescriptionBySoc ? (
                            isb.vulnerability?.resolveStatus ===
                            VulnerabilityResolveStatus.EXCEPTION ? (
                              <Button
                                onClick={() => {
                                  setCompanyExceptionOrResolveDialog(true);
                                }}
                              >
                                Approve for Exception
                              </Button>
                            ) : (
                              "-"
                            )
                          ) : (
                            "-"
                          )}
                          {isb?.vulnerability
                            ?.vulnerabilityClosureDescriptionBySoc ? (
                            <Button
                              onClick={() => {
                                setCompanyClosureDialog(true);
                              }}
                            >
                              Vulnerability Approve/reassess
                            </Button>
                          ) : null}
                        </SheetFooter>
                      </SheetContent>
                    </Sheet>
                  </TableCell>
                  <TableCell className="  ">
                    {isb.vulnerability?.resolveStatus ===
                    VulnerabilityResolveStatus.EXCEPTION ? (
                      isb.vulnerability?.companyExceptionApproval ===
                      CompanyExceptionApproval.ACCEPTED ? (
                        <Badge className="py-2 w-52 pl-8">
                          {isb.vulnerability?.companyExceptionApproval.toUpperCase()}{" "}
                          EXCEPTION
                        </Badge>
                      ) : isb.vulnerability?.companyExceptionApproval ===
                        CompanyExceptionApproval.REJECTED ? (
                        <Badge className=" py-2 w-52 pl-8 bg-red-800 hover:bg-red-500">
                          {isb.vulnerability?.companyExceptionApproval.toUpperCase()}{" "}
                          EXCEPTION
                        </Badge>
                      ) : null
                    ) : isb.vulnerability?.resolveStatus ===
                      VulnerabilityResolveStatus.RESOLVED ? (
                      isb.vulnerability
                        ?.vulnerabilityClosureCompanyApprovalStatus ? (
                        isb.vulnerability
                          ?.vulnerabilityClosureCompanyApprovalStatus ===
                        VulnerabilityClosureStatus.OPEN ? (
                          <Badge className=" py-2 w-52 pl-7 tracking-tighter ">
                            APPROVED FOR REASSESSMENT
                          </Badge>
                        ) : isb.vulnerability
                            ?.vulnerabilityClosureCompanyApprovalStatus ===
                          VulnerabilityClosureStatus.CLOSED ? (
                          <Badge className=" py-2 w-52 pl-7 tracking-tighter ">
                            APPROVED FOR CLOSURE
                          </Badge>
                        ) : (
                          <Minus size={30} className="ml-12" />
                        )
                      ) : null
                    ) : isb.vulnerability
                        ?.vulnerabilityClosureCompanyApprovalStatus ===
                      VulnerabilityClosureStatus.CLOSED ? (
                      <Badge className=" py-2 w-52 pl-7 tracking-tighter ">
                        APPROVED FOR CLOSURE
                      </Badge>
                    ) : (
                      <Minus size={30} className="ml-12" />
                    )}
                  </TableCell>
                  <TableCell className="  ">
                    {isb.vulnerability?.vulnerabilityClosureStatus ===
                    VulnerabilityClosureStatus.OPEN ? (
                      <Flag
                        color="red"
                        size={50}
                        className="shadow-md shadow-red-900 border-red-900 border rounded-full  "
                      />
                    ) : isb.vulnerability?.vulnerabilityClosureStatus ===
                      VulnerabilityClosureStatus.CLOSED ? (
                      <CheckCheck color="green" size={30} className="ml-12" />
                    ) : (
                      <Minus size={30} className="ml-12" />
                    )}
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Eye />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="min-w-[1000px]">
                        <DialogHeader className="text-xl font-bold pl-40 underline">
                          Vulnerability Details
                        </DialogHeader>
                        {isb.cvssComparison ? (
                          <div className="p-6 space-y-6">
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                              <h3 className="text-lg font-semibold text-blue-800 mb-3">
                                CVSS Comparison Analysis
                              </h3>

                              {/* CVE Information */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div className="bg-white p-3 rounded border">
                                  <h4 className="font-medium text-gray-700 mb-2">
                                    CVE Details
                                  </h4>
                                  <div className="space-y-1 text-sm">
                                    <p>
                                      <span className="font-medium">
                                        CVE ID:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.cveId || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        CVSS Score:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.cvssScore || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        Published:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.published || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        Source:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.source || "N/A"}
                                    </p>
                                  </div>
                                </div>

                                <div className="bg-white p-3 rounded border">
                                  <h4 className="font-medium text-gray-700 mb-2">
                                    Risk Calculator Results
                                  </h4>
                                  <div className="space-y-1 text-sm">
                                    <p>
                                      <span className="font-medium">
                                        Base Score:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.calculatorData?.baseScore || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        Risk Before:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.calculatorData?.riskLevelBefore ||
                                        "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        Risk After:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.calculatorData?.riskLevelAfter ||
                                        "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        Delta Score:
                                      </span>{" "}
                                      {(isb.cvssComparison as any)?.nvdData
                                        ?.calculatorData?.deltaScore || "N/A"}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Description */}
                              {(isb.cvssComparison as any)?.nvdData
                                ?.description && (
                                <div className="bg-white p-3 rounded border">
                                  <h4 className="font-medium text-gray-700 mb-2">
                                    Vulnerability Description
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {
                                      (isb.cvssComparison as any).nvdData
                                        .description
                                    }
                                  </p>
                                </div>
                              )}

                              {/* Comments */}
                              {(isb.cvssComparison as any)?.comments && (
                                <div className="bg-white p-3 rounded border">
                                  <h4 className="font-medium text-gray-700 mb-2">
                                    Analysis Comments
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {(isb.cvssComparison as any).comments}
                                  </p>
                                </div>
                              )}

                              {/* Asset and Vulnerability Status */}
                              <div className="flex flex-wrap gap-4 text-sm">
                                <div className="bg-white px-3 py-1 rounded border">
                                  <span className="font-medium">Asset:</span>{" "}
                                  {(isb.cvssComparison as any)?.asset || "N/A"}
                                </div>
                                <div
                                  className={`px-3 py-1 rounded border ${
                                    (isb.cvssComparison as any)?.isVulnerability
                                      ? "bg-red-100 text-red-800"
                                      : "bg-green-100 text-green-800"
                                  }`}
                                >
                                  <span className="font-medium">Status:</span>{" "}
                                  {(isb.cvssComparison as any)?.isVulnerability
                                    ? "Vulnerability"
                                    : "No Vulnerability"}
                                </div>
                                {(isb.cvssComparison as any)?.createdAt && (
                                  <div className="bg-white px-3 py-1 rounded border">
                                    <span className="font-medium">
                                      Created:
                                    </span>{" "}
                                    {format(
                                      new Date(
                                        (isb.cvssComparison as any).createdAt
                                      ),
                                      "MMM d, yyyy"
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="p-4">
                            <p className="text-sm text-gray-500">
                              No CVSS comparison data available
                            </p>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                  <TableCell>
                    <Sheet>
                      <SheetTrigger>
                        <Button>
                          <Eye />
                        </Button>
                      </SheetTrigger>
                      <SheetContent className="min-w-[1500px] overflow-y-auto">
                        <SheetDescription></SheetDescription>
                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory
                            </TableHead>
                            <TableCell>
                              {isb?.submission?.question?.input.label}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Inventory Details
                            </TableHead>
                            <TableCell>{isb.submission.answer}</TableCell>
                          </TableRow>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                MSA/SOW Remediation Timelines
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {isb.vulnerability?.cvssScore}
                              </TableCell>
                              <TableCell>{isb.vulnerability?.score}</TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  isb?.vulnerability?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {isb?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                              <TableCell>
                                {isb.vendor?.criticalityLevels?.map(
                                  (c, idx) => (
                                    <div key={idx}>
                                      {isb.vulnerability?.criticalityLevel ===
                                      c.level
                                        ? `${c.timeDuration} Days`
                                        : ""}
                                    </div>
                                  )
                                )}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Created Date :
                            </TableHead>
                            <TableCell>
                              {/* @ts-ignore */}
                              {isb.vulnerability?.createdAt
                                ? format(
                                    // @ts-ignore
                                    isb.vulnerability?.createdAt,
                                    "MMM-d-yyyy"
                                  )
                                : "-"}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Description:
                            </TableHead>
                            <TableCell className="w-[1000px]">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: isb.vulnerability?.remarks as string,
                                }}
                                className="text-lg"
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Acceptance by Vendor:
                            </TableHead>
                            <TableCell>
                              {isb.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.ACCEPT
                                ? "Vendor acknowledged for remediation(s)"
                                : isb.vendorAcceptanceStatus ===
                                  VendorAcceptanceStatus.REJECT
                                ? " Vendor identified false positive"
                                : null}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className="text-black font-semibold  ">
                              Vulnerability Acceptance Description:
                            </TableHead>
                            <TableCell>{isb?.vendorRejectReason}</TableCell>
                          </TableRow>
                          {isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.RESOLVED ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Resolved Date:
                                </TableHead>
                                <TableCell>
                                  {isb?.vulnerability?.resolvedDate
                                    ? format(
                                        isb?.vulnerability?.resolvedDate,
                                        "MMM - d -yyyy"
                                      )
                                    : null}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Days Vulnerability Resolved:
                                </TableHead>
                                <TableCell>
                                  {daysDifference ?? "-"} Days
                                </TableCell>
                              </TableRow>
                            </>
                          ) : (
                            ""
                          )}
                          {isb.vulnerability?.resolveStatus ===
                          VulnerabilityResolveStatus.EXCEPTION ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vendor Exception Reason:
                                </TableHead>
                                <TableCell>
                                  {isb.vulnerability?.resolveDescription}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Soc Exception Reason:
                                </TableHead>
                                <TableCell>
                                  {
                                    isb.vulnerability
                                      ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vendor Exception Evidence:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityResolvedOrExceptionEvidence as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Soc Exception Evidence:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}

                          {isb.vulnerability
                            ?.vulnerabilityClosureDescriptionBySoc ? (
                            <>
                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Description By Soc:
                                </TableHead>
                                <TableCell>
                                  {
                                    isb.vulnerability
                                      ?.vulnerabilityClosureDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>

                              <TableRow>
                                <TableHead className="text-black font-semibold  ">
                                  Vulnerability Closer Evidence By Soc:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: isb.vulnerability
                                            ?.vulnerabilityClosureEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}
                        </Table>
                      </SheetContent>
                    </Sheet>
                  </TableCell>
                </TableRow>
              );
            })}
        </TableBody>
      </Table>
    </div>
  );
}
