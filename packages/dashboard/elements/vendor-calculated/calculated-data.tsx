"use client";

import { useState } from "react";
import { trpc } from "@/providers/Providers";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Calculator,
  Calendar,
  User,
  Building,
  BarChart3,
  TrendingDown,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  Eye,
  Settings,
  FileText,
  Activity,
} from "lucide-react";
import { format } from "date-fns";

export default function VendorCalculatedData({
  assessment,
  company,
  vendor,
  onSelectionChange,
  selectedCalculation: externalSelectedCalculation,
}: {
  company: string;
  vendor: string;
  assessment: string;
  onSelectionChange?: (calculation: any) => void;
  selectedCalculation?: any;
}) {
  const [selectedCalculation, setSelectedCalculation] = useState<any | null>(
    null
  );

  // Query calculator data with filters
  const {
    data: calculatorData,
    isLoading,
    error,
    refetch,
  } = trpc.calculator.getCalculatorData.useQuery(
    {
      assessment,
      vendor,
      company,
    },
    {
      enabled: !!assessment && !!vendor,
      refetchOnWindowFocus: false,
    }
  );

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      case "moderate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "very high":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-3" />
          <p className="text-gray-600">Loading CVSS calculations...</p>
          <p className="text-sm text-gray-400 mt-1">
            Fetching saved risk assessments
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <div className="text-red-600 mb-4">
            <svg
              className="h-12 w-12 mx-auto mb-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="font-medium">Failed to load calculator data</p>
            <p className="text-sm text-red-500 mt-1">
              Unable to fetch CVSS calculations
            </p>
          </div>
          <Button
            onClick={() => refetch()}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-600 hover:bg-red-50"
          >
            🔄 Try Again
          </Button>
        </div>
      </div>
    );
  }

  const calculations = calculatorData?.data || [];

  if (calculations.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-md mx-auto">
          <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Risk Calculations Found
          </h3>
          <p className="text-gray-600 mb-4">
            No CVSS calculations have been saved for this assessment and vendor
            combination.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700">
            💡 <strong>Tip:</strong> Use the CVSS Calculator to perform risk
            assessments and save them for future reference.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Enhanced Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-50 border border-blue-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Calculator className="h-4 w-4 text-blue-600" />
                <p className="text-sm font-medium text-blue-600">
                  Total Calculations
                </p>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                {calculations.length}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Calculator className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 via-green-100 to-green-50 border border-green-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <BarChart3 className="h-4 w-4 text-green-600" />
                <p className="text-sm font-medium text-green-600">
                  Avg Base Score
                </p>
              </div>
              <p className="text-2xl font-bold text-green-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.baseScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-50 via-orange-100 to-orange-50 border border-orange-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <TrendingDown className="h-4 w-4 text-orange-600" />
                <p className="text-sm font-medium text-orange-600">
                  Avg Risk Reduction
                </p>
              </div>
              <p className="text-2xl font-bold text-orange-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.deltaScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 via-purple-100 to-purple-50 border border-purple-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Clock className="h-4 w-4 text-purple-600" />
                <p className="text-sm font-medium text-purple-600">
                  Latest Threat Score
                </p>
              </div>
              <p className="text-sm font-bold text-purple-900">
                {calculations.length > 0
                  ? format(
                      new Date(
                        Math.max(
                          ...calculations.map((calc: any) =>
                            new Date(calc.calculationDate).getTime()
                          )
                        )
                      ),
                      "MMM dd"
                    )
                  : "None"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Split Layout Container */}
      <div className="flex-1 bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="flex h-[600px]">
          {/* Left Panel - Calculations List */}
          <div className="w-1/2 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Calculator className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Risk Calculations
                    </h3>
                    <p className="text-xs text-gray-600">
                      Select to view details
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {calculations.length}
                </Badge>
              </div>
            </div>

            {/* Calculations List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {calculations.map((calc: any) => (
                <div
                  key={calc._id}
                  className={`relative border rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-sm ${
                    selectedCalculation?._id === calc._id
                      ? "border-blue-500 bg-blue-50 shadow-sm"
                      : "border-gray-200 hover:border-blue-300 bg-white hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    setSelectedCalculation(calc);
                    onSelectionChange?.(calc);
                  }}
                >
                  {/* Selection Indicator */}
                  {selectedCalculation?._id === calc._id && (
                    <div className="absolute top-2 right-2 h-2 w-2 bg-blue-500 rounded-full"></div>
                  )}

                  {/* Compact Header */}
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="h-6 w-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
                      <Calculator className="h-3 w-3 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 text-xs truncate">
                        {calc.name || `Calculation ${calc._id.slice(-6)}`}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {format(new Date(calc.calculationDate), "MMM dd")}
                      </p>
                    </div>
                  </div>

                  {/* Compact Risk Scores */}
                  <div className="space-y-1.5">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Base</span>
                      <div className="flex items-center gap-1">
                        <div className="h-1.5 w-8 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-green-400 to-red-500 rounded-full"
                            style={{
                              width: `${
                                (calc.riskResults.baseScore / 10) * 100
                              }%`,
                            }}
                          ></div>
                        </div>
                        <span className="text-xs font-semibold text-gray-900 min-w-[1.5rem]">
                          {calc.riskResults.baseScore.toFixed(1)}
                        </span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Reduction</span>
                      <span className="text-xs font-bold text-blue-600">
                        -{calc.riskResults.deltaScore.toFixed(1)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Level</span>
                      <Badge
                        className={`text-xs font-medium ${getRiskLevelColor(
                          calc.riskResults.riskLevelAfter
                        )}`}
                      >
                        {calc.riskResults.riskLevelAfter}
                      </Badge>
                    </div>
                  </div>

                  {/* User Info */}
                  <div className="flex items-center gap-1 text-xs text-gray-500 mt-2 pt-1 border-t border-gray-200">
                    <User className="h-3 w-3" />
                    <span className="truncate">
                      {calc.user.contact.firstName} {calc.user.contact.lastName}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Panel - Detailed Information */}
          <div className="w-1/2 flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 overflow-auto">
              <div className="flex items-center space-x-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Calculation Details
                  </h3>
                  <p className="text-xs text-gray-600">
                    {selectedCalculation
                      ? "Detailed analysis"
                      : "Select a calculation to view details"}
                  </p>
                  {selectedCalculation && (
                    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
                      <div className="border-b border-gray-200 px-6 py-4">
                        <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                          <Calculator className="h-5 w-5" />
                          Calculation Details:{" "}
                          {selectedCalculation.name ||
                            `Calculation ${selectedCalculation._id.slice(-6)}`}
                        </h3>
                      </div>
                      <div className="p-6 space-y-6">
                        {/* Basic Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Base Score
                            </p>
                            <p className="text-2xl font-bold text-blue-600">
                              {selectedCalculation.riskResults.baseScore.toFixed(
                                1
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Environmental Score
                            </p>
                            <p className="text-2xl font-bold text-orange-600">
                              {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                2
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Risk Reduction
                            </p>
                            <p className="text-2xl font-bold text-green-600">
                              {selectedCalculation.riskResults.deltaScore.toFixed(
                                2
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Final Risk Level
                            </p>
                            <Badge
                              className={`text-sm ${getRiskLevelColor(
                                selectedCalculation.riskResults.riskLevelAfter
                              )}`}
                            >
                              {selectedCalculation.riskResults.riskLevelAfter}
                            </Badge>
                          </div>
                        </div>

                        {/* CVSS Vector */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">
                            CVSS Vector
                          </p>
                          <div className="bg-gray-50 p-3 rounded-md">
                            <code className="text-sm font-mono break-all">
                              {selectedCalculation.cvssVector}
                            </code>
                          </div>
                        </div>

                        {/* Residual Vector */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">
                            Residual Vector
                          </p>
                          <div className="bg-gray-50 p-3 rounded-md">
                            <code className="text-sm font-mono break-all">
                              {selectedCalculation.riskResults.residualVector}
                            </code>
                          </div>
                        </div>

                        {/* Security Risk Overview */}
                        {selectedCalculation.securityRiskOverview && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-gray-600">
                              Security Risk Posture Overview
                            </p>
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-sm whitespace-pre-wrap">
                                {selectedCalculation.securityRiskOverview}
                              </p>
                            </div>
                          </div>
                        )}

                        {/* VendorAI Score */}
                        {selectedCalculation.vendorAIScore && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-gray-600">
                              VendorAI Score Notes
                            </p>
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-sm whitespace-pre-wrap">
                                {selectedCalculation.vendorAIScore}
                              </p>
                            </div>
                          </div>
                        )}

                        {/* Metadata */}
                        <div className="border-t pt-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Created:</span>
                              <span>
                                {format(
                                  new Date(selectedCalculation.calculationDate),
                                  "PPP"
                                )}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">By:</span>
                              <span>
                                {selectedCalculation.user.contact.firstName}{" "}
                                {selectedCalculation.user.contact.lastName}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Vendor:</span>
                              <span>{selectedCalculation.vendor.company}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
