"use client";

import { useState } from "react";
import { trpc } from "@/providers/Providers";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Calculator,
  Calendar,
  User,
  Building,
  BarChart3,
  TrendingDown,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  Eye,
  Settings,
  FileText,
  Activity,
  Info,
} from "lucide-react";
import { format } from "date-fns";

export default function VendorCalculatedData({
  assessment,
  company,
  vendor,
  onSelectionChange,
  selectedCalculation: externalSelectedCalculation,
}: {
  company: string;
  vendor: string;
  assessment: string;
  onSelectionChange?: (calculation: any) => void;
  selectedCalculation?: any;
}) {
  const [selectedCalculation, setSelectedCalculation] = useState<any | null>(
    null
  );

  // Query calculator data with filters
  const {
    data: calculatorData,
    isLoading,
    error,
    refetch,
  } = trpc.calculator.getCalculatorData.useQuery(
    {
      assessment,
      vendor,
      company,
    },
    {
      enabled: !!assessment && !!vendor,
      refetchOnWindowFocus: false,
    }
  );

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      case "moderate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "very high":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-3" />
          <p className="text-gray-600">Loading CVSS calculations...</p>
          <p className="text-sm text-gray-400 mt-1">
            Fetching saved risk assessments
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <div className="text-red-600 mb-4">
            <svg
              className="h-12 w-12 mx-auto mb-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="font-medium">Failed to load calculator data</p>
            <p className="text-sm text-red-500 mt-1">
              Unable to fetch CVSS calculations
            </p>
          </div>
          <Button
            onClick={() => refetch()}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-600 hover:bg-red-50"
          >
            🔄 Try Again
          </Button>
        </div>
      </div>
    );
  }

  const calculations = calculatorData?.data || [];

  if (calculations.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-md mx-auto">
          <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Risk Calculations Found
          </h3>
          <p className="text-gray-600 mb-4">
            No CVSS calculations have been saved for this assessment and vendor
            combination.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700">
            💡 <strong>Tip:</strong> Use the CVSS Calculator to perform risk
            assessments and save them for future reference.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Enhanced Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-50 border border-blue-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Calculator className="h-4 w-4 text-blue-600" />
                <p className="text-sm font-medium text-blue-600">
                  Total Calculations
                </p>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                {calculations.length}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Calculator className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 via-green-100 to-green-50 border border-green-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <BarChart3 className="h-4 w-4 text-green-600" />
                <p className="text-sm font-medium text-green-600">
                  Avg Base Score
                </p>
              </div>
              <p className="text-2xl font-bold text-green-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.baseScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-50 via-orange-100 to-orange-50 border border-orange-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <TrendingDown className="h-4 w-4 text-orange-600" />
                <p className="text-sm font-medium text-orange-600">
                  Avg Risk Reduction
                </p>
              </div>
              <p className="text-2xl font-bold text-orange-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.deltaScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 via-purple-100 to-purple-50 border border-purple-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Clock className="h-4 w-4 text-purple-600" />
                <p className="text-sm font-medium text-purple-600">
                  Latest Threat Score
                </p>
              </div>
              <p className="text-sm font-bold text-purple-900">
                {calculations.length > 0
                  ? format(
                      new Date(
                        Math.max(
                          ...calculations.map((calc: any) =>
                            new Date(calc.calculationDate).getTime()
                          )
                        )
                      ),
                      "MMM dd"
                    )
                  : "None"}
              </p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Split Layout Container */}
      <div className="flex-1 bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="flex h-[600px]">
          {/* Left Panel - Calculations List */}
          <div className="w-1/2 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Calculator className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Risk Calculations
                    </h3>
                    <p className="text-xs text-gray-600">
                      Select to view details
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {calculations.length}
                </Badge>
              </div>
            </div>

            {/* Calculations List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {calculations.map((calc: any, index: number) => (
                <div
                  key={calc._id}
                  className={`group relative overflow-hidden rounded-xl cursor-pointer   ${
                    selectedCalculation?._id === calc._id
                      ? "bg-blue-50 border-2 border-blue-500 shadow-lg shadow-blue-100"
                      : "bg-white border border-gray-200 hover:border-blue-400 hover:shadow-lg hover:shadow-gray-100"
                  }`}
                  onClick={() => {
                    setSelectedCalculation(calc);
                    onSelectionChange?.(calc);
                  }}
                >
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-blue-400 rounded-full -translate-y-10 translate-x-10"></div>
                    <div className="absolute bottom-0 left-0 w-16 h-16 bg-indigo-400 rounded-full translate-y-8 -translate-x-8"></div>
                  </div>

                  {/* Selection Indicator */}
                  {selectedCalculation?._id === calc._id && (
                    <div className="absolute top-3 right-3 flex items-center space-x-1">
                      <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <div className="h-1.5 w-1.5 bg-blue-400 rounded-full animate-pulse delay-75"></div>
                      <div className="h-1 w-1 bg-blue-300 rounded-full animate-pulse delay-150"></div>
                    </div>
                  )}

                  <div className="relative p-4">
                    {/* Enhanced Header */}
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="relative">
                        <div className="h-10 w-10 bg-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                          <Calculator className="h-5 w-5 text-white" />
                        </div>
                        <div className="absolute -top-1 -right-1 h-4 w-4 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-white">
                            {index + 1}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 text-sm truncate">
                          {calc.name || `Risk Assessment ${calc._id.slice(-6)}`}
                        </h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <p className="text-xs text-gray-500">
                            {format(
                              new Date(calc.calculationDate),
                              "MMM dd, yyyy"
                            )}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Risk Metrics */}
                    <div className="space-y-3">
                      {/* Base Score with Enhanced Progress Bar */}
                      <div className="bg-white/70 backdrop-blur-sm rounded-lg p-3 border border-white/50">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-xs font-medium text-gray-700 flex items-center">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Base Score
                          </span>
                          <span className="text-sm font-bold text-gray-900">
                            {calc.riskResults.baseScore.toFixed(1)}
                          </span>
                        </div>
                        <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`absolute inset-0 rounded-full transition-all duration-500 ${
                              calc.riskResults.baseScore >= 9
                                ? "bg-red-500"
                                : calc.riskResults.baseScore >= 7
                                ? "bg-orange-500"
                                : calc.riskResults.baseScore >= 4
                                ? "bg-yellow-500"
                                : "bg-green-500"
                            }`}
                            style={{
                              width: `${
                                (calc.riskResults.baseScore / 10) * 100
                              }%`,
                            }}
                          ></div>
                          <div className="absolute inset-0 bg-white/30 rounded-full animate-pulse"></div>
                        </div>
                      </div>

                      {/* Risk Metrics Grid */}
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-green-50 rounded-lg p-2 border border-green-200">
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-green-700 flex items-center">
                              <TrendingDown className="h-3 w-3 mr-1" />
                              Reduction
                            </span>
                            <span className="text-xs font-bold text-green-600">
                              -{calc.riskResults.deltaScore.toFixed(2)}
                            </span>
                          </div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-2 border border-blue-200">
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-blue-700 flex items-center">
                              <Shield className="h-3 w-3 mr-1" />
                              Level
                            </span>
                            <Badge
                              className={`text-xs font-medium px-2 py-0.5 ${getRiskLevelColor(
                                calc.riskResults.riskLevelAfter
                              )}`}
                            >
                              {calc.riskResults.riskLevelAfter}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Environmental Scores */}
                      <div className="bg-white/70 backdrop-blur-sm rounded-lg p-3 border border-white/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-gray-700 flex items-center">
                            <Activity className="h-3 w-3 mr-1" />
                            Environmental Impact
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1">
                            <div className="flex justify-between text-xs mb-1">
                              <span className="text-red-600 font-medium">
                                Before
                              </span>
                              <span className="text-red-600 font-bold">
                                {calc.riskResults.envScoreBefore.toFixed(2)}
                              </span>
                            </div>
                            <div className="h-1.5 bg-red-200 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-red-500 rounded-full transition-all duration-500"
                                style={{
                                  width: `${
                                    (calc.riskResults.envScoreBefore / 10) * 100
                                  }%`,
                                }}
                              ></div>
                            </div>
                          </div>
                          <div className="text-gray-400">
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 7l5 5m0 0l-5 5m5-5H6"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between text-xs mb-1">
                              <span className="text-green-600 font-medium">
                                After
                              </span>
                              <span className="text-green-600 font-bold">
                                {calc.riskResults.envScoreAfter.toFixed(2)}
                              </span>
                            </div>
                            <div className="h-1.5 bg-green-200 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-green-500 rounded-full transition-all duration-500"
                                style={{
                                  width: `${
                                    (calc.riskResults.envScoreAfter / 10) * 100
                                  }%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced User Info */}
                    <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200/50">
                      <div className="flex items-center space-x-2">
                        <div className="h-6 w-6 bg-gray-500 rounded-full flex items-center justify-center">
                          <User className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-xs text-gray-600 truncate">
                          {calc.user.contact.firstName}{" "}
                          {calc.user.contact.lastName}
                        </span>
                      </div>
                      <div className="text-xs text-gray-400">
                        ID: {calc._id.slice(-4)}
                      </div>
                    </div>
                  </div>

                  {/* Hover Effect Overlay */}
                  <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Panel - Detailed Information */}
          <div className="w-1/2 flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 overflow-auto">
              <div className="flex items-center space-x-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Calculation Details
                  </h3>
                  <p className="text-xs text-gray-600">
                    {selectedCalculation
                      ? "Detailed analysis"
                      : "Select a calculation to view details"}
                  </p>
                  {selectedCalculation && (
                    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
                      <div className="border-b border-gray-200 px-6 py-4">
                        <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                          <Calculator className="h-5 w-5" />
                          Calculation Details:{" "}
                          {selectedCalculation.name ||
                            `Calculation ${selectedCalculation._id.slice(-6)}`}
                        </h3>
                      </div>
                      <div className="p-6 space-y-6">
                        {/* Basic Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Base Score
                            </p>
                            <p className="text-2xl font-bold text-blue-600">
                              {selectedCalculation.riskResults.baseScore.toFixed(
                                1
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Environmental Score
                            </p>
                            <p className="text-2xl font-bold text-orange-600">
                              {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                2
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Risk Reduction
                            </p>
                            <p className="text-2xl font-bold text-green-600">
                              {selectedCalculation.riskResults.deltaScore.toFixed(
                                2
                              )}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">
                              Final Risk Level
                            </p>
                            <Badge
                              className={`text-sm ${getRiskLevelColor(
                                selectedCalculation.riskResults.riskLevelAfter
                              )}`}
                            >
                              {selectedCalculation.riskResults.riskLevelAfter}
                            </Badge>
                          </div>
                        </div>

                        {/* CVSS Vector */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">
                            CVSS Vector
                          </p>
                          <div className="bg-gray-50 p-3 rounded-md">
                            <code className="text-sm font-mono break-all">
                              {selectedCalculation.cvssVector}
                            </code>
                          </div>
                        </div>

                        {/* Residual Vector */}
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">
                            Residual Vector
                          </p>
                          <div className="bg-gray-50 p-3 rounded-md">
                            <code className="text-sm font-mono break-all">
                              {selectedCalculation.riskResults.residualVector}
                            </code>
                          </div>
                        </div>

                        {/* Security Risk Overview */}
                        {selectedCalculation.securityRiskOverview && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-gray-600">
                              Security Risk Posture Overview
                            </p>
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-sm whitespace-pre-wrap">
                                {selectedCalculation.securityRiskOverview}
                              </p>
                            </div>
                          </div>
                        )}

                        {/* VendorAI Score */}
                        {selectedCalculation.vendorAIScore && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-gray-600">
                              VendorAI Score Notes
                            </p>
                            <div className="bg-gray-50 p-3 rounded-md">
                              <p className="text-sm whitespace-pre-wrap">
                                {selectedCalculation.vendorAIScore}
                              </p>
                            </div>
                          </div>
                        )}
                        {/* Complete Risk Results Display */}
                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-200">
                          <div className="flex items-center space-x-2 mb-4">
                            <BarChart3 className="h-5 w-5 text-indigo-600" />
                            <h5 className="font-semibold text-indigo-900">
                              Complete Risk Analysis
                            </h5>
                          </div>

                          {/* Risk Scores Grid */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-white p-3 rounded-lg border border-indigo-100">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">
                                  Base Score
                                </span>
                                <span className="text-lg font-bold text-blue-600">
                                  {selectedCalculation.riskResults.baseScore.toFixed(
                                    2
                                  )}
                                </span>
                              </div>
                            </div>

                            <div className="bg-white p-3 rounded-lg border border-indigo-100">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">
                                  Risk Reduction
                                </span>
                                <span className="text-lg font-bold text-green-600">
                                  -
                                  {selectedCalculation.riskResults.deltaScore.toFixed(
                                    3
                                  )}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Environmental Scores */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-white p-3 rounded-lg border border-red-100">
                              <div className="text-center">
                                <div className="flex items-center justify-center space-x-1 mb-1">
                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                  <span className="text-sm font-medium text-red-700">
                                    Env Score Before
                                  </span>
                                </div>
                                <span className="text-xl font-bold text-red-600">
                                  {selectedCalculation.riskResults.envScoreBefore.toFixed(
                                    3
                                  )}
                                </span>
                              </div>
                            </div>

                            <div className="bg-white p-3 rounded-lg border border-green-100">
                              <div className="text-center">
                                <div className="flex items-center justify-center space-x-1 mb-1">
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                  <span className="text-sm font-medium text-green-700">
                                    Env Score After
                                  </span>
                                </div>
                                <span className="text-xl font-bold text-green-600">
                                  {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                    3
                                  )}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Likelihood Scores */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-white p-3 rounded-lg border border-orange-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-orange-700 block mb-1">
                                  Likelihood Before
                                </span>
                                <span className="text-lg font-bold text-orange-600">
                                  {selectedCalculation.riskResults.likelihoodBefore.toFixed(
                                    3
                                  )}
                                </span>
                              </div>
                            </div>

                            <div className="bg-white p-3 rounded-lg border border-blue-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-blue-700 block mb-1">
                                  Likelihood After
                                </span>
                                <span className="text-lg font-bold text-blue-600">
                                  {selectedCalculation.riskResults.likelihoodAfter.toFixed(
                                    3
                                  )}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Impact Scores */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-white p-3 rounded-lg border border-purple-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-purple-700 block mb-1">
                                  Impact Before
                                </span>
                                <span className="text-lg font-bold text-purple-600">
                                  {selectedCalculation.riskResults.impactBefore.toFixed(
                                    2
                                  )}
                                </span>
                              </div>
                            </div>

                            <div className="bg-white p-3 rounded-lg border border-teal-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-teal-700 block mb-1">
                                  Impact After
                                </span>
                                <span className="text-lg font-bold text-teal-600">
                                  {selectedCalculation.riskResults.impactAfter.toFixed(
                                    2
                                  )}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Risk Levels */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-white p-3 rounded-lg border border-gray-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-gray-700 block mb-1">
                                  Risk Level Before
                                </span>
                                <Badge
                                  className={`${getRiskLevelColor(
                                    selectedCalculation.riskResults
                                      .riskLevelBefore
                                  )}`}
                                >
                                  {
                                    selectedCalculation.riskResults
                                      .riskLevelBefore
                                  }
                                </Badge>
                              </div>
                            </div>

                            <div className="bg-white p-3 rounded-lg border border-gray-100">
                              <div className="text-center">
                                <span className="text-sm font-medium text-gray-700 block mb-1">
                                  Risk Level After
                                </span>
                                <Badge
                                  className={`${getRiskLevelColor(
                                    selectedCalculation.riskResults
                                      .riskLevelAfter
                                  )}`}
                                >
                                  {
                                    selectedCalculation.riskResults
                                      .riskLevelAfter
                                  }
                                </Badge>
                              </div>
                            </div>
                          </div>
                          {/* Risk Summary Table */}
                          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                            <h5 className="font-semibold text-gray-900 mb-3 flex items-center">
                              <BarChart3 className="h-4 w-4 mr-2 text-blue-600" />
                              Risk Analysis Summary
                            </h5>

                            <div className="overflow-hidden rounded-lg border border-gray-200">
                              <table className="w-full text-sm">
                                <tbody className="divide-y divide-gray-200">
                                  <tr className="bg-gray-50">
                                    <td className="px-4 py-3 font-medium text-gray-900">
                                      Base Score
                                    </td>
                                    <td className="px-4 py-3 text-right font-semibold text-gray-900">
                                      {selectedCalculation.riskResults.baseScore.toFixed(
                                        1
                                      )}
                                    </td>
                                  </tr>
                                  <tr className="bg-white">
                                    <td className="px-4 py-3 font-medium text-gray-900">
                                      Environmental Score (before mitigation)
                                    </td>
                                    <td className="px-4 py-3 text-right font-semibold text-red-600">
                                      {selectedCalculation.riskResults.envScoreBefore.toFixed(
                                        3
                                      )}
                                    </td>
                                  </tr>
                                  <tr className="bg-gray-50">
                                    <td className="px-4 py-3 font-medium text-gray-900">
                                      Environmental Score (after mitigation)
                                    </td>
                                    <td className="px-4 py-3 text-right font-semibold text-green-600">
                                      {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                        3
                                      )}
                                    </td>
                                  </tr>
                                  <tr className="bg-white">
                                    <td className="px-4 py-3 font-medium text-gray-900">
                                      Change after mitigation
                                    </td>
                                    <td className="px-4 py-3 text-right font-semibold text-blue-600">
                                      -
                                      {selectedCalculation.riskResults.deltaScore.toFixed(
                                        3
                                      )}
                                    </td>
                                  </tr>
                                  <tr className="bg-gray-50">
                                    <td className="px-4 py-3 font-medium text-gray-900">
                                      Risk Reduced (%)
                                    </td>
                                    <td className="px-4 py-3 text-right font-semibold text-green-600">
                                      {(
                                        (selectedCalculation.riskResults
                                          .deltaScore /
                                          selectedCalculation.riskResults
                                            .envScoreBefore) *
                                        100
                                      ).toFixed(1)}
                                      %
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>

                          {/* Risk Definitions */}
                          <div className="bg-blue-50 rounded-lg border border-blue-200 p-4 mb-4">
                            <h6 className="font-semibold text-blue-900 mb-3 flex items-center">
                              <Info className="h-4 w-4 mr-2" />
                              Risk Metrics Explained
                            </h6>
                            <ul className="space-y-2 text-sm text-blue-800">
                              <li className="flex items-start">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div>
                                  <span className="font-medium">
                                    Base Score:
                                  </span>{" "}
                                  Raw technical risk from the CVSS vector (no
                                  business context or controls).
                                </div>
                              </li>
                              <li className="flex items-start">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div>
                                  <span className="font-medium">
                                    Environmental Score (before mitigation):
                                  </span>{" "}
                                  Contextual risk before any controls are
                                  applied.
                                </div>
                              </li>
                              <li className="flex items-start">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div>
                                  <span className="font-medium">
                                    Environmental Score (after mitigation):
                                  </span>{" "}
                                  True risk after all controls and mitigations
                                  are applied.
                                </div>
                              </li>
                              <li className="flex items-start">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div>
                                  <span className="font-medium">
                                    Change after mitigation:
                                  </span>{" "}
                                  Absolute reduction due to mitigation.
                                </div>
                              </li>
                              <li className="flex items-start">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <div>
                                  <span className="font-medium">
                                    Risk Reduced (%):
                                  </span>{" "}
                                  Percent drop from pre- to post-mitigation
                                  risk.
                                </div>
                              </li>
                            </ul>
                          </div>

                          {/* Management Summary */}
                          <div className="bg-green-50 rounded-lg border border-green-200 p-4">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0">
                                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                              </div>
                              <div>
                                <h6 className="font-semibold text-green-900 mb-2">
                                  Management Summary
                                </h6>
                                <p className="text-sm text-green-800 leading-relaxed">
                                  Our technical risk (Base Score:{" "}
                                  <span className="font-semibold">
                                    {selectedCalculation.riskResults.baseScore.toFixed(
                                      1
                                    )}
                                  </span>
                                  ) is reduced to{" "}
                                  <span className="font-semibold">
                                    {selectedCalculation.riskResults.envScoreAfter.toFixed(
                                      3
                                    )}
                                  </span>{" "}
                                  after all business context and mitigations, a{" "}
                                  <span className="font-bold text-green-700">
                                    {(
                                      (selectedCalculation.riskResults
                                        .deltaScore /
                                        selectedCalculation.riskResults
                                          .envScoreBefore) *
                                      100
                                    ).toFixed(1)}
                                    %
                                  </span>{" "}
                                  reduction. The residual risk is now{" "}
                                  <span className="font-bold text-green-700">
                                    {
                                      selectedCalculation.riskResults
                                        .riskLevelAfter
                                    }
                                  </span>
                                  .
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Residual Vector */}
                          <div className="bg-white p-3 rounded-lg border border-gray-100">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Settings className="h-4 w-4 text-gray-600" />
                                <span className="text-sm font-medium text-gray-700">
                                  Residual CVSS Vector
                                </span>
                              </div>
                              <div className="bg-gray-50 p-2 rounded border">
                                <code className="text-xs font-mono text-gray-800 break-all">
                                  {
                                    selectedCalculation.riskResults
                                      .residualVector
                                  }
                                </code>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Metadata */}
                        <div className="border-t pt-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Created:</span>
                              <span>
                                {format(
                                  new Date(selectedCalculation.calculationDate),
                                  "PPP"
                                )}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">By:</span>
                              <span>
                                {selectedCalculation.user.contact.firstName}{" "}
                                {selectedCalculation.user.contact.lastName}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Vendor:</span>
                              <span>{selectedCalculation.vendor.company}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
