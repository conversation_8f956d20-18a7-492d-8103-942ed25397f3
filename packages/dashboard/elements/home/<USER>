"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { trpc } from "@/providers/Providers";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StringParam, useQueryParam, withDefault } from "use-query-params";
import CompanyAnalaytics from "../analytics/company-analytics";
import { CompanyType } from "../../../shared/types/Company";
import { Loader } from "lucide-react";

export default function CompanyDashboard() {
  const router = useRouter();
  const { data: session } = useSession();
  const [tab, setTab] = useQueryParam(
    "tab",
    withDefault(StringParam, "analytics")
  );

  const vendors = trpc.company.getCompanies.useQuery(
    {
      reportsTo: session?.user.companies?.at(0),
    },
    { enabled: !!session?.user.companies?.length }
  );

  const handleVendorDetails = (vendorId: string) => {
    router.push(`/vendor-details/${vendorId}`);
  };

  // isloading

  if (vendors.isLoading)
    return (
      <div className="container flex flex-1 justify-center items-center text-center text-2xl">
        <Loader className="animate-spin text-cyan-400" size={60} /> Loading....
      </div>
    );
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-950 via-indigo-900 to-purple-900">
      <Tabs
        defaultValue="analytics"
        value={tab}
        onValueChange={setTab}
        className="w-full"
      >
        <div className="sticky top-0 z-10 bg-white/10 backdrop-blur-md border-b border-white/20">
          <div className="container mx-auto px-6 py-4">
            <TabsList className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl p-1">
              <TabsTrigger
                value="analytics"
                className="data-[state=active]:bg-white data-[state=active]:text-indigo-900 data-[state=active]:shadow-lg text-white font-medium px-6 py-2 rounded-lg transition-all duration-200"
              >
                📊 Analytics
              </TabsTrigger>
              <TabsTrigger
                value="vendors"
                className="data-[state=active]:bg-white data-[state=active]:text-indigo-900 data-[state=active]:shadow-lg text-white font-medium px-6 py-2 rounded-lg transition-all duration-200"
              >
                🏢 Vendors
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <div className="container mx-auto px-6 py-8">
          <TabsContent value="analytics" className="mt-0">
            <CompanyAnalaytics />
          </TabsContent>

          <TabsContent value="vendors" className="mt-0">
            <div className="space-y-6">
              {/* Header Section */}
              <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">
                      Vendor Management
                    </h2>
                    <p className="text-white/70">
                      Manage and monitor your vendor relationships
                    </p>
                  </div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-2 border border-white/30">
                    <span className="text-white font-medium">
                      {vendors.data?.filter(
                        (p) => p.type === CompanyType.VENDOR
                      ).length || 0}{" "}
                      Vendors
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Table */}
              <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
                <Table className="">
                  <TableHeader>
                    <TableRow className="border-b border-white/20 hover:bg-white/5">
                      <TableHead className="text-white font-semibold py-4 px-6 text-center">
                        #
                      </TableHead>
                      <TableHead className="text-white font-semibold py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <span>🏢</span>
                          <span>Vendor Name</span>
                        </div>
                      </TableHead>
                      <TableHead className="text-white font-semibold py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <span>🏛️</span>
                          <span>Parent Company</span>
                        </div>
                      </TableHead>
                      <TableHead className="text-white font-semibold py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <span>🏭</span>
                          <span>Sector</span>
                        </div>
                      </TableHead>
                      <TableHead className="text-white font-semibold py-4 px-6 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <span>⭐</span>
                          <span>Priority</span>
                        </div>
                      </TableHead>
                      <TableHead className="text-white font-semibold py-4 px-6 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <span>⚡</span>
                          <span>Actions</span>
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {vendors.data?.map((p, idx) => {
                      if (p.type !== CompanyType.VENDOR) return null;

                      const getPriorityColor = (priority: string) => {
                        switch (priority?.toLowerCase()) {
                          case "high":
                            return "bg-red-500/20 text-red-300 border-red-500/30";
                          case "medium":
                            return "bg-yellow-500/20 text-yellow-300 border-yellow-500/30";
                          case "low":
                            return "bg-green-500/20 text-green-300 border-green-500/30";
                          default:
                            return "bg-gray-500/20 text-gray-300 border-gray-500/30";
                        }
                      };

                      return (
                        <TableRow
                          key={String(p._id)}
                          className="border-b border-white/10 hover:bg-white/5 transition-all duration-200 group"
                        >
                          <TableCell className="py-4 px-6 text-center">
                            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center text-white font-medium text-sm">
                              {idx + 1}
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                {p.company.charAt(0).toUpperCase()}
                              </div>
                              <div>
                                <div className="text-white font-medium">
                                  {p.company}
                                </div>
                                <div className="text-white/60 text-sm">
                                  Vendor
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <div className="text-blue-300 font-medium">
                              {p.reportsTo?.company || "N/A"}
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6">
                            <div className="inline-flex items-center px-3 py-1 rounded-full bg-white/10 border border-white/20 text-white text-sm">
                              {p.sector}
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <div
                              className={`inline-flex items-center px-3 py-1 rounded-full border text-sm font-medium ${getPriorityColor(
                                p.priorityLevel
                              )}`}
                            >
                              {p.priorityLevel || "Not Set"}
                            </div>
                          </TableCell>
                          <TableCell className="py-4 px-6 text-center">
                            <Button
                              onClick={() => handleVendorDetails(String(p._id))}
                              className="bg-white/20 hover:bg-white/30 text-white border border-white/30 hover:border-white/50 transition-all duration-200 backdrop-blur-sm rounded-lg px-4 py-2 font-medium group-hover:scale-105"
                            >
                              <span className="flex items-center space-x-2">
                                <span>👁️</span>
                                <span>View Details</span>
                              </span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                {/* Empty State */}
                {(!vendors.data ||
                  vendors.data.filter((p) => p.type === CompanyType.VENDOR)
                    .length === 0) && (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🏢</div>
                    <h3 className="text-xl font-semibold text-white mb-2">
                      No Vendors Found
                    </h3>
                    <p className="text-white/60">
                      Start by adding your first vendor to get started.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
