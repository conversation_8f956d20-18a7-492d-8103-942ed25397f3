"use client";
import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>L<PERSON> } from "recharts";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { z } from "zod";
import {
  CRITICALITY_LEVELS,
  PRIORITY_LEVEL,
} from "../../../shared/types/Company";
import { RouterOutput } from "../../../shared";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { endOfYear, startOfYear, subYears } from "date-fns";
import { trpc } from "@/providers/Providers";
import { FormDatePicker } from "@/components/form/FormDatePicker";
import { FormComboBoxPopover } from "@/components/form/FormComboPopover";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Filter } from "lucide-react";

const chartConfig = {
  [CRITICALITY_LEVELS.CRITICAL]: {
    label: "Critical",
    color: "#ff1c24",
  },
  [CRITICALITY_LEVELS.HIGH]: {
    label: "High",
    color: "hsl(var(--chart-2))",
  },
  [CRITICALITY_LEVELS.MEDIUM]: {
    label: "Medium",
    color: "hsl(var(--chart-3))",
  },
  [CRITICALITY_LEVELS.LOW]: {
    label: "Low",
    color: "hsl(var(--chart-4))",
  },
  [CRITICALITY_LEVELS.VERYLOW]: {
    label: "Very Low",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

export const allVendorvulnerabilityVendorsChartSchema = z.object({
  priority: z.array(z.nativeEnum(PRIORITY_LEVEL)),
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
  vendors: z.array(z.string()),
});

export type Filter = z.infer<typeof allVendorvulnerabilityVendorsChartSchema>;

export default function CumulativeVendorsVulnerabilities({
  vendors,
}: {
  vendors?: RouterOutput["company"]["getCompanies"];
}) {
  const allPriorities = Object.values(PRIORITY_LEVEL);
  const allCriticalities = Object.values(CRITICALITY_LEVELS);
  const allVendors = (vendors ?? []).map((v) => String(v._id));

  const filterMethods = useForm<Filter>({
    defaultValues: {
      criticalityLevels: allCriticalities,
      from: startOfYear(new Date()),
      to: endOfYear(new Date()),
      vendors: allVendors,
      priority: allPriorities,
    },
  });

  const priorityFilter = useWatch({
    control: filterMethods.control,
    name: "priority",
  });
  const vendorsFilter = useWatch({
    control: filterMethods.control,
    name: "vendors",
  });
  const fromFilter = useWatch({ control: filterMethods.control, name: "from" });
  const toFilter = useWatch({ control: filterMethods.control, name: "to" });
  const criticalityFilter = useWatch({
    control: filterMethods.control,
    name: "criticalityLevels",
  });

  const { data: chartData } =
    trpc.analytics.allVendorVulnerabilityChartByPriority.useQuery(
      {
        criticalityLevels: criticalityFilter,
        from: fromFilter,
        to: toFilter,
        vendors: vendorsFilter,
        priority: priorityFilter,
      },
      {
        enabled: !!vendorsFilter.length,
      }
    );
  return (
    <div className="space-y-5">
      <Sheet>
        <SheetTrigger asChild>
          <Button>
            <Filter />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent side={"left"} className="min-w-[500px]">
          <FormProvider {...filterMethods}>
            <div className="">
              <div className="flex  flex-col gap-4 items-center">
                <FormDatePicker
                  name="from"
                  label="From"
                  captionLayout="dropdown-buttons"
                  fromYear={subYears(new Date(), 10).getFullYear()}
                  toYear={new Date().getFullYear()}
                  className=""
                />
                <FormDatePicker
                  name="to"
                  label="To"
                  captionLayout="dropdown-buttons"
                  fromYear={subYears(new Date(), 10).getFullYear()}
                  toYear={new Date().getFullYear()}
                />
              </div>
              <FormComboBoxPopover
                name="vendors"
                label="Vendor"
                options={(vendors ?? []).map((c) => ({
                  label: c.company,
                  value: String(c._id),
                }))}
              />

              <div className="flex gap-4 items-center pb-4">
                <FormComboBoxPopover
                  name="priority"
                  label="Priority"
                  options={allPriorities.map((p) => ({
                    label: p,
                    value: p,
                  }))}
                />

                <FormComboBoxPopover
                  name="criticalityLevels"
                  label="Criticality level"
                  options={allCriticalities.map((c) => ({
                    label: c,
                    value: c,
                  }))}
                />
              </div>
            </div>
          </FormProvider>
        </SheetContent>
      </Sheet>

      <Card className=" border rounded-2xl shadow-white  bg-gradient-to-r from-sky-800 to-sky-950 ">
        <CardHeader className="items-center pb-0">
          <CardTitle className="text-white text-lg">
            Aggregate Vendor Risk
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 pb-0  ">
          <ChartContainer
            config={chartConfig}
            className="mx-auto aspect-square h-[324px] w-[465px] "
          >
            <PieChart data={chartData}>
              <ChartTooltip
                content={
                  <ChartTooltipContent nameKey="criticality" hideLabel />
                }
              />
              <ChartLegend
                content={
                  <ChartLegendContent
                    nameKey="criticality"
                    className=" text-white"
                  />
                }
              />
              <Pie data={chartData} dataKey="value" nameKey="criticality">
                {/* <LabelList
                  dataKey='criticality'
                  className='fill-background text-black '
                  stroke='none'
                  fontSize={12}
                  formatter={(value: keyof typeof chartConfig) =>
                    chartConfig[value]?.label
                  }
                /> */}
              </Pie>
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
