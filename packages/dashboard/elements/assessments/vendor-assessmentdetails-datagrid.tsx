import { SECTIONTYPE } from "../../../shared/types/Standard";
import { RouterOutput } from "../../../shared";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Question } from "../../../shared/types/Question";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useVendorAcceptanceForm } from "@/hooks/forms/vulnerability";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import VendorAcceptanceForm from "./vendor-acceptance.form";
import { Button } from "@/components/ui/button";
import { trpc } from "@/providers/Providers";
import { useSession } from "next-auth/react";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Check, Download, Eye, View } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn, toastPromise } from "@/lib/utils";
import { CRITICALITY_LEVELS } from "../../../shared/types/Company";
import VendorVulnerabilityResolveOrExceptionForm from "./vendor-vulnerability-resolve-or-exception.form";
import {
  EmployeeResolveOrExceptionForm,
  useEmployeeResolveOrExceptionFormMethods,
} from "@/hooks/forms/employeeResolveOrException";
import { SubmitHandler } from "react-hook-form";
import {
  CompanyExceptionApproval,
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from "../../../shared/types/AssessmentSubmission";
import { useDownload } from "@/hooks/useDownload";

export default function VendorAssessmentDetailsDatagrid({
  submissions,
  type,
  filterByQuestions,
}: {
  submissions: RouterOutput["assessment"]["getAssessmentSubmissions"];
  type: SECTIONTYPE;
  filterByQuestions?: string[];
}) {
  const { data: session } = useSession();
  const download = useDownload();

  const [vendorFormOpen, setVendorFormOpen] = useState(false);
  const [vulnerabilityResolveFormOpen, setvulnerabilityResolvFormOpen] =
    useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<string>();
  const [showHistorySheet, setShowHistorySheet] = useState(false);
  const submissionHistory =
    trpc.assessment.getAssessmentSubmissionHistory.useQuery({
      question: selectedQuestion,
    });

  const showHistory = (questionId: string) => {
    setSelectedQuestion(questionId);
    setShowHistorySheet(true);
  };

  const resolveOrExceptionVulnerability =
    trpc.assessment.updateVulnerabilityResolveStatusByEmployee.useMutation();

  const vendorAcceptanceMethods = useVendorAcceptanceForm();
  const employeeResolveOrExceptionFormMethods =
    useEmployeeResolveOrExceptionFormMethods();

  const handleResoloveOrExceptionByEmployee: SubmitHandler<
    EmployeeResolveOrExceptionForm
  > = (data) => {
    toastPromise({
      asyncFunc: resolveOrExceptionVulnerability.mutateAsync(data),
      success: "Vulnerability state updated",
      onSuccess() {
        employeeResolveOrExceptionFormMethods.reset();
        setvulnerabilityResolvFormOpen(false);
      },
    });
  };

  return (
    <div className="container1">
      <Sheet open={showHistorySheet} onOpenChange={setShowHistorySheet}>
        <SheetContent className="min-w-[70vw]">
          <ScrollArea className="ph">
            <SheetHeader>
              <SheetTitle>Submission history</SheetTitle>
            </SheetHeader>

            <div>
              <Accordion type="single" collapsible>
                {(submissionHistory.data || []).map((history, idx) => {
                  return (
                    <AccordionItem
                      value={String(history._id)}
                      key={String(history._id)}
                    >
                      <AccordionTrigger>
                        {format(history?.createdAt, "MMM d,yyyy - h:mm a")}
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead> Sender</TableHead>
                              <TableCell>
                                {history?.actionTaker?.email}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Reciepient</TableHead>

                              <TableCell>
                                {history?.actionReciver?.email}
                              </TableCell>
                            </TableRow>

                            <TableRow>
                              <TableHead>Vendor</TableHead>
                              <TableCell>{history.vendor?.company}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory</TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot?.submission
                                    ?.question?.input.label
                                }
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory Details</TableHead>
                              <TableCell>
                                {history.submissionSnapshot.submission.answer}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.cvssScore
                                }
                              </TableCell>
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.score
                                }
                              </TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {history.submissionSnapshot?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Created Date :
                              </TableHead>
                              <TableCell>
                                {/* @ts-ignore */}
                                {history.submissionSnapshot.vulnerability
                                  ?.createdAt
                                  ? format(
                                      // @ts-ignore
                                      history.submissionSnapshot.vulnerability
                                        ?.createdAt,
                                      "MMM-d-yyyy - h:mm a"
                                    )
                                  : "-"}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Vulnerability Description</TableHead>
                              <TableCell>
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      history?.submissionSnapshot?.vulnerability
                                        ?.remarks ?? "-",
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance by Vendor:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot
                                  ?.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.ACCEPT
                                  ? "Vendor acknowledged for remediation(s)"
                                  : history.submissionSnapshot
                                      ?.vendorAcceptanceStatus ===
                                    VendorAcceptanceStatus.REJECT
                                  ? " Vendor identified false positive"
                                  : null}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance Description:
                              </TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot
                                    ?.vendorRejectReason
                                }
                              </TableCell>
                            </TableRow>
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.RESOLVED ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Resolved Date:
                                  </TableHead>
                                  <TableCell>
                                    {history?.submissionSnapshot?.vulnerability
                                      ?.resolvedDate
                                      ? format(
                                          history?.submissionSnapshot
                                            ?.vulnerability?.resolvedDate,
                                          "MMM - d -yyyy - h:mm a"
                                        )
                                      : null}
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : (
                              ""
                            )}
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.EXCEPTION ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vendor Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.resolveDescription
                                    }
                                  </TableCell>
                                </TableRow>
                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow> */}

                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Evidence:
                                  </TableHead>
                                  <TableCell>
                                    <Button>
                                      <Download
                                        onClick={() =>
                                          download({
                                            fileId: history?.submissionSnapshot
                                              .vulnerability
                                              ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                          })
                                        }
                                      />
                                    </Button>
                                  </TableCell>
                                </TableRow> */}
                              </>
                            ) : null}

                            {history?.submissionSnapshot.vulnerability
                              ?.vulnerabilityClosureDescriptionBySoc ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Closer Description By Soc:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityClosureDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : null}
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Closure status by Customer:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot.vulnerability
                                  ?.vulnerabilityClosureCompanyApprovalStatus ===
                                VulnerabilityClosureStatus.CLOSED
                                  ? "Approved for closure"
                                  : history?.submissionSnapshot.vulnerability
                                      ?.vulnerabilityClosureCompanyApprovalStatus ===
                                    VulnerabilityClosureStatus.OPEN
                                  ? "Required reassessment"
                                  : "Pending"}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
      <Dialog open={vendorFormOpen} onOpenChange={setVendorFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Acknowledge/False Positive Vulnerability</DialogTitle>
          </DialogHeader>

          <VendorAcceptanceForm methods={vendorAcceptanceMethods} />
        </DialogContent>
      </Dialog>
      <Dialog
        open={vulnerabilityResolveFormOpen}
        onOpenChange={setvulnerabilityResolvFormOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle> Resolved/Exception </DialogTitle>
          </DialogHeader>
          <VendorVulnerabilityResolveOrExceptionForm
            methods={employeeResolveOrExceptionFormMethods}
            handleSubmit={handleResoloveOrExceptionByEmployee}
          />
        </DialogContent>
      </Dialog>
      {type === SECTIONTYPE.INVENTORY && (
        <Table>
          <TableHeader>
            <TableHead> S.No</TableHead>
            <TableHead>Inventory</TableHead>
            <TableHead>Vulnerability Expiry</TableHead>
            <TableHead>Vulnerability Details</TableHead>
            <TableHead>Acceptance</TableHead>
            <TableHead>Resolved/Exception </TableHead>
            <TableHead>Exception Date</TableHead>
            <TableHead>Vulnerability Closed Status</TableHead>
            <TableHead>CVE Details</TableHead>

            <TableHead>History</TableHead>
          </TableHeader>
          <TableBody>
            {submissions.map((submission, idx) => {
              const question = submission.submission.question as Question;

              const isInventory =
                submission.sectionType === SECTIONTYPE.INVENTORY;

              if (!isInventory) return null;
              return (
                <TableRow key={String(submission._id)}>
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell>{question.input.label}</TableCell>
                  <TableCell>
                    {submission.vulnerability?.expiry
                      ? format(
                          submission.vulnerability?.expiry,
                          "MMM d,yyyy - h:mm a"
                        )
                      : "-"}
                  </TableCell>
                  <TableCell>
                    <Sheet>
                      <SheetTrigger className="item-center">
                        <Badge>
                          <Eye />
                        </Badge>
                      </SheetTrigger>

                      <SheetContent className="min-w-[1250px]">
                        <ScrollArea className="h-[calc(100vh-100px)]">
                          <SheetHeader>
                            <SheetTitle>Vulnerability Details</SheetTitle>
                          </SheetHeader>
                          <Table className="border-2 ">
                            <TableRow>
                              <TableHead>Inventory :</TableHead>
                              <TableCell>{question.input.label}</TableCell>
                            </TableRow>
                            <TableRow className="border-2 ">
                              <TableHead>Vendor Inventory Details :</TableHead>
                              <TableCell>
                                {Array.isArray(submission.submission.answer)
                                  ? submission.submission.answer.map(
                                      (_, idx) => <Badge key={idx}>{_}</Badge>
                                    )
                                  : submission.submission.answer}
                              </TableCell>
                            </TableRow>
                          </Table>

                          <Table>
                            <TableHeader>
                              <TableRow className="border-2 ">
                                <TableHead className="font-bold text-black">
                                  CVSS Score :
                                </TableHead>
                                <TableHead className="font-bold text-black">
                                  Vendorai Score :
                                </TableHead>
                                <TableHead className="font-bold text-black">
                                  Vulnerability Criticality Level :
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              <TableRow className="border-2 ">
                                <TableCell>
                                  {submission?.vulnerability?.cvssScore ?? "-"}
                                </TableCell>
                                <TableCell>
                                  {submission?.vulnerability?.score ?? "-"}
                                </TableCell>
                                <TableCell
                                  className={cn(
                                    "font-bold",
                                    submission?.vulnerability
                                      ?.criticalityLevel ===
                                      CRITICALITY_LEVELS.CRITICAL &&
                                      "text-rose-700 w",
                                    submission?.vulnerability
                                      ?.criticalityLevel ===
                                      CRITICALITY_LEVELS.HIGH && "text-red-700",
                                    submission?.vulnerability
                                      ?.criticalityLevel ===
                                      CRITICALITY_LEVELS.MEDIUM &&
                                      "text-orange-600",
                                    submission?.vulnerability
                                      ?.criticalityLevel ===
                                      CRITICALITY_LEVELS.LOW &&
                                      "text-yellow-600",
                                    submission?.vulnerability
                                      ?.criticalityLevel ===
                                      CRITICALITY_LEVELS.VERYLOW &&
                                      "text-yellow-900"
                                  )}
                                >
                                  {submission?.vulnerability?.criticalityLevel.toUpperCase() ??
                                    "-"}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                          <Table>
                            <TableRow className="border-2 ">
                              <TableHead className="">
                                Vulnerability Description :
                              </TableHead>
                              <TableCell>
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: submission.vulnerability
                                      ?.remarks as string,
                                  }}
                                  className="text-lg"
                                />
                              </TableCell>
                            </TableRow>
                          </Table>
                        </ScrollArea>
                      </SheetContent>
                    </Sheet>
                  </TableCell>

                  <TableCell>
                    {submission?.vulnerability?.criticalityLevel ? (
                      <Badge
                        className={cn(
                          `py-2 w-32 cursor-pointer ${
                            submission.vendorAcceptanceStatus ===
                            VendorAcceptanceStatus.REJECT
                              ? "bg-red-500 pl-2"
                              : submission.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.ACCEPT
                              ? "bg-green-600 pl-2 "
                              : "bg-orange-400  "
                          }`
                        )}
                        onClick={() => {
                          vendorAcceptanceMethods.setValue(
                            "submissionId",
                            String(submission._id)
                          );
                          setVendorFormOpen(true);
                        }}
                      >
                        {submission?.vendorAcceptanceStatus
                          ? submission.vendorAcceptanceStatus ===
                            VendorAcceptanceStatus.REJECT
                            ? "FALSE POSITIVE"
                            : submission.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.ACCEPT
                            ? "ACKNOWLEDGED"
                            : null
                          : "ACCEPT/REJECT"}
                      </Badge>
                    ) : null}
                  </TableCell>
                  <TableCell>
                    {submission?.vendorAcceptanceStatus ? (
                      submission?.vendorAcceptanceStatus ===
                      VendorAcceptanceStatus.ACCEPT ? (
                        <div className="flex flex-row justify-center items-center">
                          <Button
                            className={cn(
                              submission.vulnerability.resolveStatus ===
                                VulnerabilityResolveStatus.RESOLVED
                                ? "bg-green-600 rounded-full h-[34px] "
                                : submission.vulnerability.resolveStatus ===
                                  VulnerabilityResolveStatus.EXCEPTION
                                ? "bg-orange-600 rounded-full h-[34px] "
                                : "rounded-full h-[34px] "
                            )}
                            onClick={() => {
                              employeeResolveOrExceptionFormMethods.setValue(
                                "submissionId",
                                String(submission._id)
                              );
                              setvulnerabilityResolvFormOpen(true);
                            }}
                          >
                            {submission.vulnerability.resolveStatus ===
                            VulnerabilityResolveStatus.RESOLVED
                              ? VulnerabilityResolveStatus.RESOLVED.toUpperCase()
                              : submission.vulnerability.resolveStatus ===
                                VulnerabilityResolveStatus.EXCEPTION
                              ? VulnerabilityResolveStatus.EXCEPTION.toUpperCase()
                              : "RESOLVE/EXCEPTION"}
                          </Button>

                          {submission.vulnerability.resolveStatus ===
                            VulnerabilityResolveStatus.EXCEPTION &&
                          submission.vulnerability.companyExceptionApproval ? (
                            <Badge className="w-30 h-8 ml-2 ">
                              {submission.vulnerability.companyExceptionApproval.toUpperCase()}
                            </Badge>
                          ) : null}
                        </div>
                      ) : null
                    ) : null}
                  </TableCell>
                  <TableCell>
                    {submission?.vulnerability?.companyExceptionApproval ===
                    CompanyExceptionApproval.ACCEPTED ? (
                      <>
                        {submission?.vulnerability?.companyExceptionEndDate
                          ? format(
                              submission?.vulnerability
                                ?.companyExceptionEndDate,
                              "MMM d,yyy - h:mm a"
                            )
                          : "-"}
                      </>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    {submission?.vulnerability?.vulnerabilityClosureStatus ? (
                      <Badge className="w-32 py-2 pl-9">
                        {submission?.vulnerability?.vulnerabilityClosureStatus.toUpperCase()}
                      </Badge>
                    ) : null}
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Eye />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="min-w-[1700px]">
                        <DialogHeader className="text-xl font-bold pl-40 underline">
                          Vulnerability Details
                        </DialogHeader>
                        {submission.cvssComparison ? (
                          <div className="p-6 space-y-6 max-h-[80vh] overflow-y-auto">
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                              <h3 className="text-xl font-bold text-blue-800 mb-6 flex items-center">
                                <span className="mr-2">🔍</span>
                                Complete CVSS Comparison Analysis
                              </h3>

                              {/* Basic Information */}
                              <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-blue-500">
                                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                  📋 Basic Information
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div className="bg-gray-50 p-3 rounded">
                                    <span className="font-medium text-gray-700">
                                      Asset:
                                    </span>
                                    <p className="text-sm mt-1">
                                      {(submission.cvssComparison as any)
                                        ?.asset || "N/A"}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* CVE Details */}
                              <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-red-500">
                                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                  🚨 CVE Details
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-3">
                                    <div className="bg-red-50 p-3 rounded">
                                      <span className="font-medium text-red-700">
                                        CVE ID:
                                      </span>
                                      <p className="text-sm mt-1 font-mono">
                                        {(submission.cvssComparison as any)
                                          ?.nvdData?.cveId || "N/A"}
                                      </p>
                                    </div>
                                    <div className="bg-red-50 p-3 rounded">
                                      <span className="font-medium text-red-700">
                                        CVSS Score:
                                      </span>
                                      <p className="mt-1 font-bold text-lg">
                                        {(submission.cvssComparison as any)
                                          ?.nvdData?.cvssScore || "N/A"}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="space-y-3">
                                    <div className="bg-red-50 p-3 rounded">
                                      <span className="font-medium text-red-700">
                                        Published:
                                      </span>
                                      <p className="text-sm mt-1">
                                        {(submission.cvssComparison as any)
                                          ?.nvdData?.published || "N/A"}
                                      </p>
                                    </div>
                                    <div className="bg-red-50 p-3 rounded">
                                      <span className="font-medium text-red-700">
                                        Source:
                                      </span>
                                      <p className="text-sm mt-1">
                                        {(submission.cvssComparison as any)
                                          ?.nvdData?.source || "N/A"}
                                      </p>
                                    </div>
                                  </div>
                                </div>

                                {/* CVE Description */}
                                {(submission.cvssComparison as any)?.nvdData
                                  ?.description && (
                                  <div className="mt-4 bg-red-50 p-4 rounded">
                                    <span className="font-medium text-red-700">
                                      Description:
                                    </span>
                                    <p className="text-sm mt-2 leading-relaxed">
                                      {
                                        (submission.cvssComparison as any)
                                          .nvdData.description
                                      }
                                    </p>
                                  </div>
                                )}

                                {/* CVE Metrics */}
                                {(submission.cvssComparison as any)?.nvdData
                                  ?.metrics && (
                                  <div className="mt-4 bg-red-50 p-4 rounded">
                                    <span className="font-medium text-red-700">
                                      CVSS Metrics:
                                    </span>
                                    <pre className="text-xs mt-2 bg-white p-3 rounded border overflow-x-auto">
                                      {JSON.stringify(
                                        (submission.cvssComparison as any)
                                          .nvdData.metrics,
                                        null,
                                        2
                                      )}
                                    </pre>
                                  </div>
                                )}
                              </div>

                              {/* Risk Calculator Results */}
                              <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-green-500">
                                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                  📊 Risk Calculator Results
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Base Score:
                                    </span>
                                    <p className="mt-1 font-bold text-lg">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData?.baseScore ||
                                        "N/A"}
                                    </p>
                                  </div>
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Environment Score Before:
                                    </span>
                                    <p className="mt-1 font-bold text-lg">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData
                                        ?.envScoreBefore || "N/A"}
                                    </p>
                                  </div>
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Environment Score After:
                                    </span>
                                    <p className="mt-1 font-bold text-lg">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData
                                        ?.envScoreAfter || "N/A"}
                                    </p>
                                  </div>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Delta Score:
                                    </span>
                                    <p className="mt-1 font-bold text-lg text-blue-600">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData?.deltaScore ||
                                        "N/A"}
                                    </p>
                                  </div>
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Risk Level Before:
                                    </span>
                                    <p className="text-sm mt-1 font-semibold">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData
                                        ?.riskLevelBefore || "N/A"}
                                    </p>
                                  </div>
                                  <div className="bg-green-50 p-3 rounded">
                                    <span className="font-medium text-green-700">
                                      Risk Level After:
                                    </span>
                                    <p className="text-sm mt-1 font-semibold">
                                      {(submission.cvssComparison as any)
                                        ?.nvdData?.calculatorData
                                        ?.riskLevelAfter || "N/A"}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Detailed Calculator Information */}
                              {(submission.cvssComparison as any)
                                ?.cvssCalculator && (
                                <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-indigo-500">
                                  <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                    🧮 Calculator Details
                                  </h4>

                                  {/* Calculator Basic Info */}
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div className="bg-indigo-50 p-3 rounded">
                                      <span className="font-medium text-indigo-700">
                                        Calculator Name:
                                      </span>
                                      <p className="text-sm mt-1">
                                        {(submission.cvssComparison as any)
                                          .cvssCalculator.name ||
                                          "Unnamed Calculator"}
                                      </p>
                                    </div>
                                    <div className="bg-indigo-50 p-3 rounded">
                                      <span className="font-medium text-indigo-700">
                                        CVSS Vector:
                                      </span>
                                      <p className="text-xs mt-1 font-mono break-all">
                                        {(submission.cvssComparison as any)
                                          .cvssCalculator.cvssVector || "N/A"}
                                      </p>
                                    </div>
                                  </div>

                                  {/* CVSS Metrics */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.cvssMetrics && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        CVSS Metrics:
                                      </h5>
                                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                                        {Object.entries(
                                          (submission.cvssComparison as any)
                                            .cvssCalculator.cvssMetrics
                                        )
                                          .filter(([key]) => key !== "_id")
                                          .map(([key, value]) => (
                                            <div
                                              key={key}
                                              className="bg-indigo-50 p-2 rounded text-center"
                                            >
                                              <div className="text-xs font-medium text-indigo-600">
                                                {key}
                                              </div>
                                              <div className="text-sm font-bold">
                                                {String(value) || "N/A"}
                                              </div>
                                            </div>
                                          ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Risk Inputs */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.riskInputs && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        Risk Inputs:
                                      </h5>
                                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        {Object.entries(
                                          (submission.cvssComparison as any)
                                            .cvssCalculator.riskInputs
                                        )
                                          .filter(([key]) => key !== "_id")
                                          .map(([key, value]) => (
                                            <div
                                              key={key}
                                              className="bg-indigo-50 p-3 rounded"
                                            >
                                              <span className="text-xs font-medium text-indigo-600 capitalize">
                                                {key
                                                  .replace(/([A-Z])/g, " $1")
                                                  .trim()}
                                                :
                                              </span>
                                              <p className="text-sm font-semibold mt-1">
                                                {typeof value === "boolean"
                                                  ? value
                                                    ? "Yes"
                                                    : "No"
                                                  : String(value)}
                                              </p>
                                            </div>
                                          ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Complete Risk Results */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.riskResults && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        Complete Risk Results:
                                      </h5>
                                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        {Object.entries(
                                          (submission.cvssComparison as any)
                                            .cvssCalculator.riskResults
                                        )
                                          .filter(([key]) => key !== "_id")
                                          .map(([key, value]) => (
                                            <div
                                              key={key}
                                              className="bg-indigo-50 p-3 rounded"
                                            >
                                              <span className="text-xs font-medium text-indigo-600 capitalize">
                                                {key
                                                  .replace(/([A-Z])/g, " $1")
                                                  .trim()}
                                                :
                                              </span>
                                              <p className="text-sm font-semibold mt-1">
                                                {String(value)}
                                              </p>
                                            </div>
                                          ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Security Risk Overview */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.securityRiskOverview && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        Security Risk Overview:
                                      </h5>
                                      <div className="bg-indigo-50 p-3 rounded">
                                        <p className="text-sm">
                                          {
                                            (submission.cvssComparison as any)
                                              .cvssCalculator
                                              .securityRiskOverview
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  )}

                                  {/* Vendor AI Score */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.vendorAIScore && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        Vendor AI Score:
                                      </h5>
                                      <div className="bg-indigo-50 p-3 rounded">
                                        <p className="text-sm font-semibold">
                                          {
                                            (submission.cvssComparison as any)
                                              .cvssCalculator.vendorAIScore
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  )}

                                  {/* Mitigation Controls */}
                                  {(submission.cvssComparison as any)
                                    .cvssCalculator.mitigationControls && (
                                    <div className="mb-4">
                                      <h5 className="font-medium text-indigo-700 mb-2">
                                        Mitigation Controls:
                                      </h5>
                                      <div className="space-y-3">
                                        {Object.entries(
                                          (submission.cvssComparison as any)
                                            .cvssCalculator.mitigationControls
                                        ).map(([category, controls]) => (
                                          <div
                                            key={category}
                                            className="bg-indigo-50 p-3 rounded"
                                          >
                                            <h6 className="font-medium text-indigo-600 mb-2">
                                              {category}:
                                            </h6>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                              {Array.isArray(controls) &&
                                                controls.map(
                                                  (
                                                    control: any,
                                                    index: number
                                                  ) => (
                                                    <div
                                                      key={index}
                                                      className="flex items-center space-x-2"
                                                    >
                                                      <span
                                                        className={`text-xs px-2 py-1 rounded ${
                                                          control.checked
                                                            ? "bg-green-200 text-green-800"
                                                            : "bg-gray-200 text-gray-600"
                                                        }`}
                                                      >
                                                        {control.checked
                                                          ? "✓"
                                                          : "✗"}
                                                      </span>
                                                      <span className="text-sm">
                                                        {control.name}
                                                      </span>
                                                    </div>
                                                  )
                                                )}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Analysis Comments */}
                              {(submission.cvssComparison as any)?.comments && (
                                <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-purple-500">
                                  <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                    💬 Analysis Comments
                                  </h4>
                                  <div className="bg-purple-50 p-4 rounded">
                                    <p className="text-sm leading-relaxed">
                                      {
                                        (submission.cvssComparison as any)
                                          .comments
                                      }
                                    </p>
                                  </div>
                                </div>
                              )}

                              {/* Status and Timestamps */}
                              <div className="bg-white rounded-lg p-4 mb-6 border-l-4 border-yellow-500">
                                <h4 className="text-lg font-semibold text-gray-800 mb-3">
                                  ⏰ Status & Timestamps
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div
                                    className={`p-3 rounded ${
                                      (submission.cvssComparison as any)
                                        ?.isVulnerability
                                        ? "bg-red-100"
                                        : "bg-green-100"
                                    }`}
                                  >
                                    <span className="font-medium text-gray-700">
                                      Vulnerability Status:
                                    </span>
                                    <p
                                      className={`text-sm mt-1 font-bold ${
                                        (submission.cvssComparison as any)
                                          ?.isVulnerability
                                          ? "text-red-800"
                                          : "text-green-800"
                                      }`}
                                    >
                                      {(submission.cvssComparison as any)
                                        ?.isVulnerability
                                        ? "🚨 VULNERABILITY IDENTIFIED"
                                        : "✅ NO VULNERABILITY"}
                                    </p>
                                  </div>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                  {(submission.cvssComparison as any)
                                    ?.createdAt && (
                                    <div className="bg-yellow-50 p-3 rounded">
                                      <span className="font-medium text-yellow-700">
                                        Created:
                                      </span>
                                      <p className="text-sm mt-1">
                                        {format(
                                          new Date(
                                            (
                                              submission.cvssComparison as any
                                            ).createdAt
                                          ),
                                          "PPP 'at' p"
                                        )}
                                      </p>
                                    </div>
                                  )}
                                  {(submission.cvssComparison as any)
                                    ?.updatedAt && (
                                    <div className="bg-yellow-50 p-3 rounded">
                                      <span className="font-medium text-yellow-700">
                                        Last Updated:
                                      </span>
                                      <p className="text-sm mt-1">
                                        {format(
                                          new Date(
                                            (
                                              submission.cvssComparison as any
                                            ).updatedAt
                                          ),
                                          "PPP 'at' p"
                                        )}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Raw Data (for debugging/complete info) */}
                            </div>
                          </div>
                        ) : (
                          <div className="p-6">
                            <div className="text-center py-8">
                              <div className="text-gray-400 text-6xl mb-4">
                                📊
                              </div>
                              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                                No CVSS Comparison Data
                              </h3>
                              <p className="text-sm text-gray-500">
                                No comparison analysis has been performed for
                                this vulnerability yet.
                              </p>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      onClick={() =>
                        showHistory(
                          submission.submission.question?._id as string
                        )
                      }
                    >
                      View History
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
      {type === SECTIONTYPE.GENERAL && (
        <Table className="text-xs">
          <TableHeader>
            <TableHead>Question</TableHead>
            <TableHead>Information</TableHead>
            <TableHead>Audit Score</TableHead>
            <TableHead>Auditor Remarks</TableHead>
            {/* <TableHead>Acceptance</TableHead> */}
            <TableHead>History</TableHead>
          </TableHeader>
          <TableBody>
            {submissions.map((submission) => {
              const question = submission.submission.question as Question;

              const isGeneral = submission.sectionType === SECTIONTYPE.GENERAL;

              if (!isGeneral) return null;
              return (
                <TableRow key={String(submission._id)}>
                  <TableCell>{question.input.label}</TableCell>

                  <TableCell className="flex gap-2">
                    {Array.isArray(submission.submission.answer)
                      ? submission.submission.answer.map((_, idx) => (
                          <Badge key={idx}>{_}</Badge>
                        ))
                      : submission.submission.answer}
                  </TableCell>

                  <TableCell>{submission.audit?.score ?? "-"}</TableCell>
                  <TableCell>{submission.audit?.remarks ?? "-"}</TableCell>

                  <TableCell>
                    <Button
                      variant="outline"
                      onClick={() =>
                        showHistory(
                          submission.submission.question?._id as string
                        )
                      }
                    >
                      View History
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
