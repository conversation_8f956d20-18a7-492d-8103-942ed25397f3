import FormField from "@/components/form/FormField";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toastPromise } from "@/lib/utils";
import { trpc } from "@/providers/Providers";
import { zodResolver } from "@hookform/resolvers/zod";
import { updateAssetForAssessmentValidator } from "packages/shared/validators/assessment-submission.validator";
import { useState } from "react";
import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn,
} from "react-hook-form";
import { z } from "zod";

type Form = z.infer<typeof updateAssetForAssessmentValidator>;

type Props = {
  methods: UseFormReturn<Form>;
  onSubmit: SubmitHandler<Form>;
};

export const useUpdateAssetFormMethods = () => {
  const { mutateAsync } =
    trpc.assessment.updateAssetForAssessment.useMutation();
  const methods = useForm<Form>({
    resolver: zodResolver(updateAssetForAssessmentValidator),
    defaultValues: {
      _id: "",
      asset: "",
    },
  });

  const onSubmit = (data: Form) => {
    toastPromise({
      asyncFunc: mutateAsync(data),
      success: "Asset updated successfully",
      onSuccess() {
        methods.reset();
      },
    });
  };

  return { methods, onSubmit };
};

export default function AssetUpdateForm({ methods, onSubmit }: Props) {
  const [confirm, setConfirm] = useState(false);
  return (
    <div>
      <FormProvider {...methods}>
        <form onClick={methods.handleSubmit(onSubmit)}>
          <FormField name="asset" label="Asset" placeholder="Enter asset" />
          {!confirm && (
            <Button
              type="button"
              onClick={() => setConfirm(true)}
              className="mt-10"
            >
              Update
            </Button>
          )}
          {confirm && (
            <div className="flex gap-3 items-center justify-center mt-10">
              <Label>
                Are you sure you want to clone this asset for monitoring?
              </Label>
              <Button type="submit">Confirm</Button>
            </div>
          )}
        </form>
      </FormProvider>
    </div>
  );
}
