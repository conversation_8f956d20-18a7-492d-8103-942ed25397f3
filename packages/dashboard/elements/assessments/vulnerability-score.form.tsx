import FormField from "@/components/form/FormField";
import { FormSelect } from "@/components/form/FormSelect";
import { VulnerabilityFormProps } from "@/hooks/forms/vulnerability";
import { CRITICALITY_LEVELS } from "../../../shared/types/Company";
import { FormProvider } from "react-hook-form";
import { FormTextField } from "@/components/form/FormTextField";
import { Button } from "@/components/ui/button";
import FormFilePicker from "@/components/form/FormFilePicker";
import { format } from "date-fns";
import FormRichTextEditor from "@/components/form/FormRichTextEditor";
import { trpc } from "@/providers/Providers";

export default function VulnerabilityScoreForm({
  methods,
  handleSubmit,
  handleSocSubmitAndEmail,
  assessment,
  vendor,
  company,
  asset,
}: VulnerabilityFormProps) {
  const { data } = trpc.saveNVD.getNVDData.useQuery({
    assessment,
    company,
    vendor,
    asset,
  });

  return (
    <form className="space-y-4 ">
      <FormProvider {...methods}>
        <FormField
          name="cvssScore"
          label="CVSS Score"
          placeholder="Enter Cvss Score"
        />
        <FormField
          name="score"
          label="VendorAi Score"
          placeholder="Enter score"
        />
        <FormSelect
          name="criticalityLevel"
          label="Criticality Level"
          placeholder="Select Criticality Level"
          options={Object.entries(CRITICALITY_LEVELS).map(([label, value]) => ({
            label,
            value,
          }))}
        />
        <FormRichTextEditor name="remarks" label="Vulnerability Remarks" />
        {/* <FormTextField
          name='remarks'
          label='Remarks'
          placeholder='Enter remarks if any'
          className='min-h-[400px]'
        /> */}
        <FormFilePicker
          fileName={`Vulnerability Raised Evidence ${format(
            new Date(),
            "PPP hh:mm a"
          )}`}
          name="vulnerabilityEvidence"
        />
        <FormSelect
          name="cvssComparison"
          label="Comparison"
          options={[
            { label: data?.asset ?? "", value: data?._id.toString() ?? "no" },
          ]}
        />
        <Button onClick={methods.handleSubmit(handleSubmit)}>Save</Button>
        <Button
          className="ml-4"
          onClick={methods.handleSubmit(handleSocSubmitAndEmail)}
        >
          Save & email
        </Button>
      </FormProvider>
    </form>
  );
}
